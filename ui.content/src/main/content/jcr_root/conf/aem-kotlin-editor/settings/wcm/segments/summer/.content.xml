<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0"
    jcr:primaryType="cq:Page">
    <jcr:content
        jcr:primaryType="cq:PageContent"
        jcr:title="Summer"
        sling:resourceType="cq/contexthub/components/segment-page"
        segmentBoost="{Long}0"
        segmentName="Season Summer">
        <traits
            jcr:primaryType="nt:unstructured"
            sling:resourceType="cq/contexthub/components/traits/logic/operator-or">
            <orpar
                jcr:primaryType="nt:unstructured"
                sling:resourceType="foundation/components/parsys">
                <one
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="cq/contexthub/components/traits/logic/operator-and">
                    <andpar
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="foundation/components/parsys">
                        <location
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="greater-than">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="geolocation/latitude"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="40"/>
                        </location>
                        <fromMonth
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="greater-than-or-equal">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="surferinfo/month"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="4"/>
                        </fromMonth>
                        <toMonth
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="less-than">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="surferinfo/month"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="9"/>
                        </toMonth>
                    </andpar>
                </one>
                <two
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="cq/contexthub/components/traits/logic/operator-and">
                    <andpar
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="foundation/components/parsys">
                        <location
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="less-than">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="geolocation/latitude"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="-38"/>
                        </location>
                        <dates
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/logic/operator-or">
                            <orpar
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="foundation/components/parsys">
                                <beforeMonth
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                                    dataType="number"
                                    operator="less-than">
                                    <left
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                        property="surferinfo/month"/>
                                    <right
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                        value="4"/>
                                </beforeMonth>
                                <afterMonth
                                    jcr:primaryType="nt:unstructured"
                                    sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                                    dataType="number"
                                    operator="greater-than-or-equal">
                                    <left
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                        property="surferinfo/month"/>
                                    <right
                                        jcr:primaryType="nt:unstructured"
                                        sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                        value="9"/>
                                </afterMonth>
                            </orpar>
                        </dates>
                    </andpar>
                </two>
                <three
                    jcr:primaryType="nt:unstructured"
                    sling:resourceType="cq/contexthub/components/traits/logic/operator-and">
                    <andpar
                        jcr:primaryType="nt:unstructured"
                        sling:resourceType="foundation/components/parsys">
                        <fromLocation
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="greater-than">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="geolocation/latitude"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="-38"/>
                        </fromLocation>
                        <toLocation
                            jcr:primaryType="nt:unstructured"
                            sling:resourceType="cq/contexthub/components/traits/generic-comparison/variants/property-value"
                            dataType="number"
                            operator="less-than">
                            <left
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/property"
                                property="geolocation/latitude"/>
                            <right
                                jcr:primaryType="nt:unstructured"
                                sling:resourceType="cq/contexthub/components/traits/generic-comparison/type/value"
                                value="40"/>
                        </toLocation>
                    </andpar>
                </three>
            </orpar>
        </traits>
    </jcr:content>
</jcr:root>
