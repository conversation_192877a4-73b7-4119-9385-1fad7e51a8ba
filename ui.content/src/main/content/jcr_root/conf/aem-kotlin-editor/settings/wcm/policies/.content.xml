<?xml version="1.0" encoding="UTF-8"?>
<jcr:root xmlns:sling="http://sling.apache.org/jcr/sling/1.0" xmlns:cq="http://www.day.com/jcr/cq/1.0" xmlns:jcr="http://www.jcp.org/jcr/1.0" xmlns:nt="http://www.jcp.org/jcr/nt/1.0" xmlns:rep="internal"
    jcr:mixinTypes="[rep:AccessControllable]"
    jcr:primaryType="cq:Page">
    <rep:policy/>
    <aem-kotlin-editor jcr:primaryType="nt:unstructured">
        <components jcr:primaryType="nt:unstructured">
            <form jcr:primaryType="nt:unstructured">
                <container jcr:primaryType="nt:unstructured">
                    <form-container
                        jcr:description="Allows the form components to be dropped into this core form container."
                        jcr:primaryType="nt:unstructured"
                        jcr:title="Content Form"
                        sling:resourceType="wcm/core/components/policy/policy"
                        components="[/apps/aem-kotlin-editor/components/form/button,/apps/aem-kotlin-editor/components/form/hidden,/apps/aem-kotlin-editor/components/form/options,/apps/aem-kotlin-editor/components/form/text]">
                        <jcr:content jcr:primaryType="nt:unstructured"/>
                    </form-container>
                </container>
            </form>
            <languagenavigation jcr:primaryType="nt:unstructured">
                <policy_xfm
                    jcr:primaryType="nt:unstructured"
                    jcr:title="AEM Kotlin Editor Language Navigation - Experience Fragment"
                    sling:resourceType="wcm/core/components/policy/policy"
                    navigationRoot="/content/aem-kotlin-editor"
                    structureDepth="1">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy_xfm>
            </languagenavigation>
            <image jcr:primaryType="nt:unstructured">
                <policy_651483963895698
                    jcr:description="- Enable lazy loading for faster page loading.&#xd;&#xa;- Define allowed image widths for automatic loading of the image in the most optimal resolution, given the size at which each image is displayed on the page, and the visitor's pixel density.&#xd;&#xa;- Disallow direct asset upload, to force authors to first upload the images to Assets."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Content Image"
                    sling:resourceType="wcm/core/components/policy/policy"
                    allowedRenditionWidths="[320,480,600,800,1024,1200,1600]"
                    disableLazyLoading="false"
                    enableAssetDelivery="true"
                    allowUpload="false"
                    altValueFromDAM="true"
                    displayPopupTitle="true"
                    isDecorative="false"
                    jpegQuality="{Long}85"
                    titleValueFromDAM="true"
                    uuidDisabled="true">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                    <plugins jcr:primaryType="nt:unstructured">
                        <crop
                            jcr:primaryType="nt:unstructured"
                            features="*">
                            <aspectRatios jcr:primaryType="nt:unstructured">
                                <item0
                                    jcr:primaryType="nt:unstructured"
                                    name="Wide Landscape"
                                    ratio="0.6180"/>
                                <item1
                                    jcr:primaryType="nt:unstructured"
                                    name="Landscape"
                                    ratio="0.8284"/>
                                <item2
                                    jcr:primaryType="nt:unstructured"
                                    name="Square"
                                    ratio="1"/>
                                <item3
                                    jcr:primaryType="nt:unstructured"
                                    name="Portrait"
                                    ratio="1.6180"/>
                            </aspectRatios>
                        </crop>
                        <rotate
                            jcr:primaryType="nt:unstructured"
                            features="right"/>
                        <flip
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                    </plugins>
                </policy_651483963895698>
            </image>
            <text jcr:primaryType="nt:unstructured">
                <policy_641562756958017
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Content Text"
                    sling:resourceType="wcm/core/components/policy/policy">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                    <rtePlugins jcr:primaryType="nt:unstructured">
                        <paraformat
                            jcr:primaryType="nt:unstructured"
                            features="*">
                            <formats
                                jcr:primaryType="nt:unstructured"
                                override="true">
                                <item0
                                    jcr:primaryType="nt:unstructured"
                                    description="Paragraph"
                                    tag="p"/>
                                <item1
                                    jcr:primaryType="nt:unstructured"
                                    description="Quote"
                                    tag="blockquote"/>
                                <item2
                                    jcr:primaryType="nt:unstructured"
                                    description="Preformatted"
                                    tag="pre"/>
                            </formats>
                        </paraformat>
                        <misctools
                            jcr:primaryType="nt:unstructured"
                            features="-">
                            <specialCharsConfig jcr:primaryType="nt:unstructured">
                                <chars
                                    jcr:primaryType="nt:unstructured"
                                    override="true">
                                    <item0
                                        jcr:primaryType="nt:unstructured"
                                        entity="&amp;copy;"
                                        name="copyright"/>
                                    <item1
                                        jcr:primaryType="nt:unstructured"
                                        entity="&amp;euro;"
                                        name="euro"/>
                                    <item2
                                        jcr:primaryType="nt:unstructured"
                                        entity="&amp;reg;"
                                        name="registered"/>
                                    <item3
                                        jcr:primaryType="nt:unstructured"
                                        entity="&amp;trade;"
                                        name="trademark"/>
                                </chars>
                            </specialCharsConfig>
                        </misctools>
                        <edit
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                        <findreplace
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                        <undo
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                        <spellcheck
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                        <image
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                        <table
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                        <lists
                            jcr:primaryType="nt:unstructured"
                            features="*"/>
                        <justify
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                        <format
                            jcr:primaryType="nt:unstructured"
                            features="bold,italic"/>
                        <links
                            jcr:primaryType="nt:unstructured"
                            features="modifylink,unlink"/>
                        <subsuperscript
                            jcr:primaryType="nt:unstructured"
                            features="-"/>
                    </rtePlugins>
                </policy_641562756958017>
            </text>
            <title jcr:primaryType="nt:unstructured">
                <policy_641475696923109
                    jcr:description="Allows only H1 and disallows links for the main page title."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Page Title"
                    sling:resourceType="wcm/core/components/policy/policy"
                    allowedTypes="h1"
                    linkDisabled="true"
                    type="h1">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy_641475696923109>
                <policy_641528232375303
                    jcr:description="Allows all sizes, but not H1, which is reserved for the main page title."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Content Title"
                    sling:resourceType="wcm/core/components/policy/policy"
                    allowedTypes="[h2,h3,h4,h5,h6]"
                    linkDisabled="false"
                    type="h2">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy_641528232375303>
            </title>
            <experiencefragment jcr:primaryType="nt:unstructured">
                <policy_header
                    cq:styleDefaultElement="header"
                    jcr:description="Sets a &lt;header> element on the page header experience fragment."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Page Header"
                    sling:resourceType="wcm/core/components/policy/policy">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy_header>
                <policy_footer
                    cq:styleDefaultElement="footer"
                    jcr:description="Sets a &lt;footer> element on the page footer experience fragment."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Page Footer"
                    sling:resourceType="wcm/core/components/policy/policy">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy_footer>
            </experiencefragment>
            <container jcr:primaryType="nt:unstructured">
                <policy_1574694950110
                    jcr:description="Allows the template components and defines the component mapping (this configures what components should be automatically created when authors drop assets from the content finder to the page editor)."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Page Root"
                    sling:resourceType="wcm/core/components/policy/policy"
                    components="[group:AEM Kotlin Editor - Content,/apps/aem-kotlin-editor/components/form/container,group:AEM Kotlin Editor - Structure]">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                    <cq:authoring jcr:primaryType="nt:unstructured">
                        <assetToComponentMapping jcr:primaryType="nt:unstructured">
                            <mapping_1575024218483
                                jcr:primaryType="nt:unstructured"
                                assetGroup="media"
                                assetMimetype="image/*"
                                droptarget="image"
                                resourceType="aem-kotlin-editor/components/image"/>
                            <mapping_1575030843388
                                jcr:primaryType="nt:unstructured"
                                assetGroup="content"
                                assetMimetype="text/html"
                                droptarget="experiencefragment"
                                resourceType="aem-kotlin-editor/components/experiencefragment"/>
                            <mapping_1575030853128
                                jcr:primaryType="nt:unstructured"
                                assetGroup="media"
                                assetMimetype="[text/html,application/vnd.adobe.contentfragment]"
                                droptarget="contentfragment"
                                resourceType="aem-kotlin-editor/components/contentfragment"/>
                        </assetToComponentMapping>
                    </cq:authoring>
                </policy_1574694950110>
                <policy_1574695586800
                    jcr:description="Allows the page components and defines the component mapping (this configures what components should be automatically created when authors drop assets from the content finder to the page editor)."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Page Content"
                    sling:resourceType="wcm/core/components/policy/policy"
                    components="[group:AEM Kotlin Editor - Content,/apps/aem-kotlin-editor/components/form/container]">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                    <cq:authoring jcr:primaryType="nt:unstructured">
                        <assetToComponentMapping jcr:primaryType="nt:unstructured">
                            <mapping_1575030255082
                                jcr:primaryType="nt:unstructured"
                                assetGroup="media"
                                assetMimetype="image/*"
                                droptarget="image"
                                resourceType="aem-kotlin-editor/components/image"/>
                            <mapping_1575030260142
                                jcr:primaryType="nt:unstructured"
                                assetGroup="content"
                                assetMimetype="text/html"
                                droptarget="experiencefragment"
                                resourceType="aem-kotlin-editor/components/experiencefragment"/>
                            <mapping_1575030269139
                                jcr:primaryType="nt:unstructured"
                                assetGroup="media"
                                assetMimetype="[text/html,application/vnd.adobe.contentfragment]"
                                droptarget="contentfragment"
                                resourceType="aem-kotlin-editor/components/contentfragment"/>
                        </assetToComponentMapping>
                    </cq:authoring>
                </policy_1574695586800>
                <policy_649128221558427
                    cq:styleDefaultElement="main"
                    jcr:description="Sets a &lt;main> element on the page content area."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Page Main"
                    sling:resourceType="wcm/core/components/policy/policy">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy_649128221558427>
                <policy_1575040440977
                    jcr:description="Allows the template components and defines the component mapping (this configures what components should be automatically created when authors drop assets from the content finder to the page editor)."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="XF Root"
                    sling:resourceType="wcm/core/components/policy/policy"
                    components="[group:AEM Kotlin Editor - Content,/apps/aem-kotlin-editor/components/form/container]">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                    <cq:authoring jcr:primaryType="nt:unstructured">
                        <assetToComponentMapping jcr:primaryType="nt:unstructured">
                            <mapping_1575024218483
                                jcr:primaryType="nt:unstructured"
                                assetGroup="media"
                                assetMimetype="image/*"
                                droptarget="image"
                                resourceType="aem-kotlin-editor/components/image"/>
                            <mapping_1575030843388
                                jcr:primaryType="nt:unstructured"
                                assetGroup="content"
                                assetMimetype="text/html"
                                droptarget="experiencefragment"
                                resourceType="aem-kotlin-editor/components/experiencefragment"/>
                            <mapping_1575030853128
                                jcr:primaryType="nt:unstructured"
                                assetGroup="media"
                                assetMimetype="[text/html,application/vnd.adobe.contentfragment]"
                                droptarget="contentfragment"
                                resourceType="aem-kotlin-editor/components/contentfragment"/>
                        </assetToComponentMapping>
                    </cq:authoring>
                </policy_1575040440977>
            </container>
            <teaser jcr:primaryType="nt:unstructured">
                <policy_1575031387650
                    jcr:description="Sets the title size to H3."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Content Teaser"
                    sling:resourceType="wcm/core/components/policy/policy"
                    titleType="h3">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy_1575031387650>
            </teaser>
            <download jcr:primaryType="nt:unstructured">
                <policy_1575032193319
                    jcr:description="Sets the title size to H3."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Content Download"
                    sling:resourceType="wcm/core/components/policy/policy"
                    allowUpload="false"
                    displayFilename="true"
                    displayFormat="true"
                    displaySize="true"
                    titleType="h3">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy_1575032193319>
            </download>
            <page jcr:primaryType="nt:unstructured">
                <policy
                    jcr:description="Includes the required client libraries."
                    jcr:primaryType="nt:unstructured"
                    jcr:title="Generic Page"
                    sling:resourceType="wcm/core/components/policy/policy"
                    clientlibs="[aem-kotlin-editor.dependencies,aem-kotlin-editor.site]"
                    clientlibsJsHead="aem-kotlin-editor.dependencies">
                    <jcr:content jcr:primaryType="nt:unstructured"/>
                </policy>
            </page>
        </components>
    </aem-kotlin-editor>
    <wcm jcr:primaryType="nt:unstructured">
        <foundation jcr:primaryType="nt:unstructured">
            <components jcr:primaryType="nt:unstructured">
                <responsivegrid jcr:primaryType="nt:unstructured">
            
                </responsivegrid>
            </components>
        </foundation>
    </wcm>
</jcr:root>
