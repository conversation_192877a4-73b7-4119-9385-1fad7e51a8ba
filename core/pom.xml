<?xml version="1.0" encoding="UTF-8"?>
<!--
 |  Copyright 2017 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.aemkotlineditor</groupId>
        <artifactId>aem-kotlin-editor</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>aem-kotlin-editor.core</artifactId>
    <name>AEM Kotlin Editor - Core</name>
    <description>Core bundle for AEM Kotlin Editor</description>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.sling</groupId>
                <artifactId>sling-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>biz.aQute.bnd</groupId>
                <artifactId>bnd-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>bnd-process</id>
                        <goals>
                            <goal>bnd-process</goal>
                        </goals>
                        <configuration>
                            <bnd><![CDATA[
# The public API of our bundle.
Export-Package: \
    com.aemkotlineditor.core.scripts,\
    com.aemkotlineditor.core.services,\
    com.aemkotlineditor.core.models,\
    com.aemkotlineditor.core.exceptions

# Hide embedded Kotlin packages to prevent import resolution issues
Private-Package: \
    kotlin.*,\
    org.jetbrains.kotlin.*,\
    com.aemkotlineditor.core.services.impl,\
    com.aemkotlineditor.core.servlets

# Explicitly import ONLY what OUR OWN code needs from the OSGi framework.
# Exclude problematic packages that cause resolution issues
Import-Package: \
    javax.script,\
    org.slf4j;version="[1.7,2)",\
    !android.os,\
    !com.sun.*,\
    !jdk.internal.*,\
    !kotlin.script.experimental.*,\
    !kotlinx.coroutines.*,\
    !kotlinx.serialization.*,\
    !org.checkerframework.*,\
    !org.jetbrains.annotations,\
    !org.jetbrains.kotlin.com.*,\
    !org.jetbrains.kotlin.io.*,\
    !org.jetbrains.kotlin.org.*,\
    !org.mozilla.*,\
    !sun.*,\
    !org.jetbrains.kotlin.cli.*,\
    *;resolution:=optional

# Embed only the minimal required Kotlin JARs
-includeresource: \
    lib/kotlin-stdlib.jar=kotlin-stdlib-*.jar;filter:=!/META-INF/versions/9/module-info.class,\
    lib/kotlin-script-runtime.jar=kotlin-script-runtime-*.jar,\
    lib/kotlin-scripting-jsr223.jar=kotlin-scripting-jsr223-*.jar

# Add the embedded JARs to the bundle's internal classpath
Bundle-ClassPath: .,\
    lib/kotlin-stdlib.jar,\
    lib/kotlin-script-runtime.jar,\
    lib/kotlin-scripting-jsr223.jar

# Handle split packages from embedded Kotlin JARs
-split-package: merge-first

# Suppress common warnings
-fixupmessages: "Classes found in the wrong directory*";is:=warning
                                ]]></bnd>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>biz.aQute.bnd</groupId>
                <artifactId>bnd-baseline-maven-plugin</artifactId>
                <configuration>
                    <failOnMissing>false</failOnMissing>
                </configuration>
                <executions>
                    <execution>
                        <id>baseline</id>
                        <goals>
                            <goal>baseline</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>uk.org.lidalia</groupId>
            <artifactId>slf4j-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.adobe.aem</groupId>
            <artifactId>aem-sdk-api</artifactId>
        </dependency>
        <!-- Minimal Kotlin dependencies for JSR-223 scripting -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-script-runtime</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-scripting-jsr223</artifactId>
            <scope>compile</scope>
        </dependency>
        <!-- JetBrains annotations for @Nullable, @NotNull, etc. -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.adobe.cq</groupId>
            <artifactId>core.wcm.components.core</artifactId>
            <scope>test</scope>
        </dependency>


        <!-- Testing -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit-addons</groupId>
            <artifactId>junit-addons</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.wcm</groupId>
            <artifactId>io.wcm.testing.aem-mock.junit5</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.sling</groupId>
            <artifactId>org.apache.sling.testing.caconfig-mock-plugin</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.adobe.cq</groupId>
            <artifactId>core.wcm.components.testing.aem-mock-plugin</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Required to be able to support injection with @Self and @Via -->
        <dependency>
            <groupId>org.apache.sling</groupId>
            <artifactId>org.apache.sling.models.impl</artifactId>
            <version>1.4.14</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
