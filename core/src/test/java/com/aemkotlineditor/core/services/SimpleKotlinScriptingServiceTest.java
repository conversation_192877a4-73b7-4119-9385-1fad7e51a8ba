package com.aemkotlineditor.core.services;

import com.aemkotlineditor.core.services.impl.SimpleKotlinScriptingServiceImpl;
import org.apache.sling.api.resource.ResourceResolver;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for SimpleKotlinScriptingService to verify basic functionality.
 */
@ExtendWith(MockitoExtension.class)
class SimpleKotlinScriptingServiceTest {

    @Mock
    private ResourceResolver resourceResolver;

    private SimpleKotlinScriptingService scriptingService;

    @BeforeEach
    void setUp() {
        scriptingService = new SimpleKotlinScriptingServiceImpl();
        // Note: In real OSGi environment, activation happens automatically
        // For unit tests, we'll test the service as-is without manual activation
    }

    @Test
    void testServiceActivation() {
        // Test that the service activates and engine availability can be checked
        // Note: Engine might not be available in test environment, but service should not crash
        assertDoesNotThrow(() -> {
            boolean available = scriptingService.isEngineAvailable();
            // Just verify the method doesn't throw an exception
            assertNotNull(available);
        });
    }

    @Test
    void testScriptExecutionWithoutEngine() {
        // Test graceful handling when engine is not available
        String result = scriptingService.executeScript(resourceResolver, "println(\"test\")");
        
        // Should return an error message, not throw an exception
        assertNotNull(result);
        assertTrue(result.contains("ERROR") || result.contains("Script executed successfully"));
    }

    @Test
    void testNullParameterHandling() {
        // Test that null parameters are handled gracefully
        assertDoesNotThrow(() -> {
            String result = scriptingService.executeScript(null, "println(\"test\")");
            assertNotNull(result);
        });
    }
}
