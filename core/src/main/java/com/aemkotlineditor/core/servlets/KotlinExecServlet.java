package com.aemkotlineditor.core.servlets;

import com.aemkotlineditor.core.models.ExecutionResult;
import com.aemkotlineditor.core.services.KotlinScriptingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.servlets.HttpConstants;
import org.apache.sling.api.servlets.SlingAllMethodsServlet;
import org.apache.sling.servlets.annotations.SlingServletResourceTypes;
import org.jetbrains.annotations.NotNull;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Reference;

import javax.servlet.Servlet;
import javax.servlet.ServletException;
import java.io.IOException;

/**
 * A Sling Servlet that serves as the HTTP endpoint for executing Kotlin scripts.
 * <p>
 * This servlet listens for POST requests on pages with the resource type 'aem-kotlin-editor/components/editor-page'.
 * It extracts the script content from the request, delegates the execution to the {@link KotlinScriptingService},
 * and returns the result as a JSON object.
 */
@Component(service = Servlet.class)
@SlingServletResourceTypes(
        resourceTypes = "aem-kotlin-editor/components/editor-page",
        methods = HttpConstants.METHOD_POST,
        selectors = "exec",
        extensions = "json"
)
public class KotlinExecServlet extends SlingAllMethodsServlet {

    /**
     * Injects the OSGi service that provides the core script execution logic.
     * The {@code @Reference} annotation ensures that a running instance of KotlinScriptingService
     * is available to this servlet before it can be activated. The 'transient' keyword
     * is a good practice to prevent accidental serialization.
     */
    @Reference
    private transient KotlinScriptingService kotlinScriptingService;

    /**
     * Handles the HTTP POST request sent from the editor's frontend.
     * Both request and response objects are guaranteed by the Sling framework to be non-null.
     *
     * @param request The Sling HTTP request object, containing the script parameter. Guaranteed non-null by the container.
     * @param response The Sling HTTP response object, used to send the JSON result back to the client. Guaranteed non-null by the container.
     * @throws ServletException If a servlet-specific error occurs.
     * @throws IOException If an input or output error is detected when the servlet handles the request.
     */
    @Override
    protected void doPost(@NotNull SlingHttpServletRequest request, @NotNull SlingHttpServletResponse response) throws ServletException, IOException {
        // The 'script' parameter from the request could be null, so we must validate it.
        String script = request.getParameter("script");
        if (script == null || script.trim().isEmpty()) {
            response.setStatus(SlingHttpServletResponse.SC_BAD_REQUEST);
            response.getWriter().write("{\"error\":\"Script parameter is missing or empty.\"}");
            return;
        }

        // Delegate the actual work to the scripting service.
        // We know from its contract that 'execute' returns a @NotNull result, so we don't need a null check here.
        ExecutionResult result = kotlinScriptingService.execute(request.getResourceResolver(), script);

        // Serialize the result POJO to JSON and write it to the response.
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        new ObjectMapper().writeValue(response.getWriter(), result);
    }
}
