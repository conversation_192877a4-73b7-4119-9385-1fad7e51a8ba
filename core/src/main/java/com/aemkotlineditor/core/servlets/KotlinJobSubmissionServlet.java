package com.aemkotlineditor.core.servlets;

import com.aemkotlineditor.core.exceptions.KotlinScriptingException;
import com.aemkotlineditor.core.services.JobBuilderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.servlets.SlingAllMethodsServlet;
import org.jetbrains.annotations.NotNull;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Servlet;
import javax.servlet.ServletException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.apache.sling.api.servlets.HttpConstants.METHOD_POST;

/**
 * Servlet that serves as a manual trigger for submitting a new Kotlin Script Job.
 * It handles submissions from both a JCR path and direct text input from an editor,
 * delegating the actual job creation to the {@link JobBuilderService}.
 */
@Component(service = Servlet.class, property = {
        "sling.servlet.methods=" + METHOD_POST,
        "sling.servlet.paths=/bin/kotlin/submitJob",
        "sling.auth.requirements=/bin/kotlin/submitJob"
})
public class KotlinJobSubmissionServlet extends SlingAllMethodsServlet {

    private static final Logger LOG = LoggerFactory.getLogger(KotlinJobSubmissionServlet.class);

    @Reference
    private transient JobBuilderService jobBuilderService;

    /**
     * Handles the HTTP POST request to submit a new job.
     * It expects either a 'scriptPath' or a 'scriptText' request parameter.
     * CSRF protection is handled automatically by AEM's CSRF filter.
     *
     * @param request The Sling HTTP request object.
     * @param response The Sling HTTP response object.
     * @throws IOException If an I/O error occurs.
     * @throws ServletException If a servlet error occurs.
     */
    @Override
    protected void doPost(@NotNull SlingHttpServletRequest request, @NotNull SlingHttpServletResponse response) throws IOException, ServletException {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        ObjectMapper objectMapper = new ObjectMapper();

        // Validate CSRF token (additional check beyond AEM's automatic protection)
        if (!isValidCsrfToken(request)) {
            LOG.warn("CSRF token validation failed for job submission request");
            response.setStatus(SlingHttpServletResponse.SC_FORBIDDEN);
            objectMapper.writeValue(response.getWriter(), Map.of(
                "status", "error",
                "message", "CSRF token validation failed. Please refresh the page and try again."
            ));
            return;
        }

        try {
            String scriptPath = request.getParameter("scriptPath");
            String scriptText = request.getParameter("scriptText");

            String jobId;
            // In a real implementation, you would also pass a map of configuration parameters from the request
            Map<String, Object> config = new HashMap<>();

            if (scriptPath != null && !scriptPath.isBlank()) {
                jobId = jobBuilderService.createAndQueueJob(scriptPath, config);
            } else if (scriptText != null && !scriptText.isBlank()) {
                jobId = jobBuilderService.createAndQueueJobFromText(scriptText, config);
            } else {
                response.setStatus(SlingHttpServletResponse.SC_BAD_REQUEST);
                objectMapper.writeValue(response.getWriter(), Map.of("status", "error", "message", "Request must contain either 'scriptPath' or 'scriptText' parameter."));
                return;
            }

            objectMapper.writeValue(response.getWriter(), Map.of("status", "success", "jobId", jobId));

        } catch (KotlinScriptingException e) {
            LOG.error("Failed to create and queue job.", e);
            response.setStatus(SlingHttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), Map.of("status", "error", "message", e.getMessage()));
        }
    }

    /**
     * Validates the CSRF token from the request.
     * Checks both the header and request parameter for the token.
     *
     * @param request The Sling HTTP request
     * @return true if the CSRF token is valid, false otherwise
     */
    private boolean isValidCsrfToken(@NotNull SlingHttpServletRequest request) {
        // Get CSRF token from header (preferred method)
        String csrfToken = request.getHeader("CSRF-Token");

        // Fallback: get from request parameter
        if (csrfToken == null || csrfToken.trim().isEmpty()) {
            csrfToken = request.getParameter("CSRF-Token");
        }

        // If no token provided, it's invalid
        if (csrfToken == null || csrfToken.trim().isEmpty()) {
            return false;
        }

        // Get the expected token from the request attributes (set by AEM's CSRF filter)
        Object expectedToken = request.getAttribute("csrf_token");

        // If no expected token, check if CSRF protection is disabled
        if (expectedToken == null) {
            // In some AEM configurations, CSRF might be disabled or handled differently
            // Log this case but don't fail the request
            LOG.debug("No CSRF token found in request attributes. CSRF protection may be disabled.");
            return true;
        }

        // Compare tokens
        return csrfToken.equals(expectedToken.toString());
    }
}