package com.aemkotlineditor.core.servlets;

import com.aemkotlineditor.core.exceptions.KotlinScriptingException;
import com.aemkotlineditor.core.services.JobBuilderService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.servlets.SlingAllMethodsServlet;
import org.jetbrains.annotations.NotNull;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Servlet;
import javax.servlet.ServletException;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.apache.sling.api.servlets.HttpConstants.METHOD_POST;

/**
 * Servlet that serves as a manual trigger for submitting a new Kotlin Script Job.
 * It handles submissions from both a JCR path and direct text input from an editor,
 * delegating the actual job creation to the {@link JobBuilderService}.
 */
@Component(service = Servlet.class, property = {
        "sling.servlet.methods=" + METHOD_POST,
        "sling.servlet.paths=/bin/kotlin/submitJob"
})
public class KotlinJobSubmissionServlet extends SlingAllMethodsServlet {

    private static final Logger LOG = LoggerFactory.getLogger(KotlinJobSubmissionServlet.class);

    @Reference
    private transient JobBuilderService jobBuilderService;

    /**
     * Handles the HTTP POST request to submit a new job.
     * It expects either a 'scriptPath' or a 'scriptText' request parameter.
     *
     * @param request The Sling HTTP request object.
     * @param response The Sling HTTP response object.
     * @throws IOException If an I/O error occurs.
     */
    @Override
    protected void doPost(@NotNull SlingHttpServletRequest request, @NotNull SlingHttpServletResponse response) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            String scriptPath = request.getParameter("scriptPath");
            String scriptText = request.getParameter("scriptText");

            String jobId;
            // In a real implementation, you would also pass a map of configuration parameters from the request
            Map<String, Object> config = new HashMap<>();

            if (scriptPath != null && !scriptPath.isBlank()) {
                jobId = jobBuilderService.createAndQueueJob(scriptPath, config);
            } else if (scriptText != null && !scriptText.isBlank()) {
                jobId = jobBuilderService.createAndQueueJobFromText(scriptText, config);
            } else {
                response.setStatus(SlingHttpServletResponse.SC_BAD_REQUEST);
                objectMapper.writeValue(response.getWriter(), Map.of("status", "error", "message", "Request must contain either 'scriptPath' or 'scriptText' parameter."));
                return;
            }

            objectMapper.writeValue(response.getWriter(), Map.of("status", "success", "jobId", jobId));

        } catch (KotlinScriptingException e) {
            LOG.error("Failed to create and queue job.", e);
            response.setStatus(SlingHttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            objectMapper.writeValue(response.getWriter(), Map.of("status", "error", "message", e.getMessage()));
        }
    }
}