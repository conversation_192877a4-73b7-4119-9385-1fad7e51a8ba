package com.aemkotlineditor.core.servlets;

import com.aemkotlineditor.core.services.SimpleKotlinScriptingService;
import org.apache.sling.api.SlingHttpServletRequest;
import org.apache.sling.api.SlingHttpServletResponse;
import org.apache.sling.api.servlets.HttpConstants;
import org.apache.sling.api.servlets.SlingSafeMethodsServlet;
import org.apache.sling.servlets.annotations.SlingServletPaths;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Reference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Servlet;
import javax.servlet.ServletException;
import java.io.IOException;

/**
 * Simple test servlet for Kotlin scripting functionality.
 */
@Component(service = Servlet.class)
@SlingServletPaths(value = "/bin/kotlin-test")
public class SimpleKotlinTestServlet extends SlingSafeMethodsServlet {

    private static final Logger LOG = LoggerFactory.getLogger(SimpleKotlinTestServlet.class);

    @Reference
    private SimpleKotlinScriptingService kotlinService;

    @Override
    protected void doGet(SlingHttpServletRequest request, SlingHttpServletResponse response) 
            throws ServletException, IOException {
        
        response.setContentType("text/plain");
        response.setCharacterEncoding("UTF-8");
        
        StringBuilder output = new StringBuilder();
        output.append("=== Kotlin Scripting Service Test ===\n\n");
        
        // Check if service is available
        if (kotlinService == null) {
            output.append("ERROR: Kotlin scripting service is not available\n");
        } else {
            output.append("✓ Kotlin scripting service is available\n");
            
            // Check if engine is ready
            if (kotlinService.isEngineAvailable()) {
                output.append("✓ Kotlin script engine is ready\n\n");
                
                // Test simple script execution
                String testScript = request.getParameter("script");
                if (testScript == null || testScript.trim().isEmpty()) {
                    testScript = "println(\"Hello from Kotlin!\")";
                }
                
                output.append("Executing script: ").append(testScript).append("\n");
                output.append("--- Output ---\n");
                
                try {
                    String result = kotlinService.executeScript(request.getResourceResolver(), testScript);
                    output.append(result);
                } catch (Exception e) {
                    output.append("ERROR: ").append(e.getMessage());
                    LOG.error("Script execution failed", e);
                }
                
            } else {
                output.append("✗ Kotlin script engine is NOT ready\n");
            }
        }
        
        response.getWriter().write(output.toString());
    }
}
