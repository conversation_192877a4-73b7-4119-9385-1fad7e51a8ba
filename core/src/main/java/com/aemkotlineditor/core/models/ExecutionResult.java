package com.aemkotlineditor.core.models;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * A Plain Old Java Object (POJO) that serves as a Data Transfer Object (DTO)
 * for the results of a Kotlin script execution.
 * <p>
 * This class encapsulates all relevant information from a script run, such as the
 * console output, any errors that occurred, and the total execution time. It is
 * designed to be easily serialized into a JSON object for transmission back to the
 * client-side web interface.
 */
public class ExecutionResult {

    private final String output;
    private final String error;
    private final long executionTimeMs;

    /**
     * Constructs a new ExecutionResult.
     *
     * @param output The text captured from the script's standard output (e.g., from {@code out.println()}).
     * @param error A string representation of any exception or error message that occurred during execution.
     * This will be null or empty if the script ran successfully.
     * @param executionTimeMs The total time in milliseconds that the script took to execute.
     */
    public ExecutionResult(String output, String error, long executionTimeMs) {
        this.output = output;
        this.error = error;
        this.executionTimeMs = executionTimeMs;
    }

    /**
     * Gets the captured standard output from the script.
     *
     * @return The script's console output.
     */
    @JsonProperty
    public String getOutput() {
        return output;
    }

    /**
     * Gets the error message, if any.
     *
     * @return The error message as a string, or null/empty if no error occurred.
     */
    @JsonProperty
    public String getError() {
        return error;
    }

    /**
     * Gets the total execution time of the script.
     *
     * @return The execution duration in milliseconds.
     */
    @JsonProperty
    public long getExecutionTimeMs() {
        return executionTimeMs;
    }
}