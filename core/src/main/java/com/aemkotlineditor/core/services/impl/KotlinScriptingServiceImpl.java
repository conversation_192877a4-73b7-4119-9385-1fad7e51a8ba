package com.aemkotlineditor.core.services.impl;

import com.aemkotlineditor.core.exceptions.KotlinScriptingException;
import com.aemkotlineditor.core.models.ExecutionResult;
import com.aemkotlineditor.core.services.KotlinScriptingService;
import com.day.cq.tagging.TagManager;
import com.day.cq.wcm.api.PageManager;
import org.apache.sling.api.resource.ResourceResolver;
import org.jetbrains.annotations.NotNull;
import org.osgi.service.component.annotations.Activate;
import org.osgi.service.component.annotations.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import javax.script.SimpleBindings;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.UUID;

/**
 * The core implementation of the {@link KotlinScriptingService}.
 * <p>
 * This OSGi component is responsible for the actual execution of Kotlin scripts.
 * It initializes the JSR 223 Kotlin ScriptEngine, prepares the execution environment
 * by binding essential AEM objects, runs the script, and safely captures all output
 * and errors.
 */
@Component(service = KotlinScriptingService.class, immediate = true)
public class KotlinScriptingServiceImpl implements KotlinScriptingService {

    private static final Logger LOG = LoggerFactory.getLogger(KotlinScriptingServiceImpl.class);

    private ScriptEngine engine;

    /**
     * The activate method is called by the OSGi framework when the component is started.
     * It initializes the Kotlin {@link ScriptEngine}. Using {@code @Activate} with
     * {@code immediate = true} ensures that the potentially slow engine initialization
     * happens at bundle startup rather than on the first request, improving performance.
     * <p>
     * A custom classloader handling is necessary here because the OSGi framework's default
     * classloader may not be able to find the Kotlin ScriptEngineFactory. We temporarily set
     * the thread's context classloader to this bundle's classloader, which contains the
     * embedded Kotlin libraries.
     */
    @Activate
    protected void activate() {
        ClassLoader currentClassLoader = Thread.currentThread().getContextClassLoader();
        Thread.currentThread().setContextClassLoader(this.getClass().getClassLoader());
        try {
            ScriptEngineManager factory = new ScriptEngineManager();
            this.engine = factory.getEngineByName("kotlin");
            if (this.engine == null) {
                LOG.error("Kotlin ScriptEngine not found. This is a critical error. Ensure dependencies are correctly embedded in the OSGi bundle.");
            } else {
                LOG.info("Kotlin ScriptEngine loaded successfully.");
            }
        } catch(Exception e) {
            LOG.error("Failed to initialize Kotlin ScriptEngine", e);
        } finally {
            Thread.currentThread().setContextClassLoader(currentClassLoader);
        }
    }

    /**
     * {@inheritDoc}
     * This implementation guarantees a non-null return value.
     */
    @Override
    @NotNull
    public ExecutionResult execute(@NotNull ResourceResolver resourceResolver, @NotNull String script) {
        if (engine == null) {
            // This is a system-level failure, so we throw an exception.
            throw new KotlinScriptingException("CRITICAL: Kotlin Script Engine not available. Check server logs.");
        }

        long startTime = System.currentTimeMillis();
        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        // Create the bindings that will be available as global variables within the script.
        SimpleBindings bindings = new SimpleBindings();
        bindings.put("resourceResolver", resourceResolver);
        bindings.put("pageManager", resourceResolver.adaptTo(PageManager.class));
        bindings.put("tagManager", resourceResolver.adaptTo(TagManager.class));
        bindings.put("log", LOG);
        bindings.put("out", printWriter); // This allows the script to write output using out.println(...)

        try {
            engine.eval(script, bindings);
            long endTime = System.currentTimeMillis();
            // On success, return a result with a null error field.
            return new ExecutionResult(stringWriter.toString(), null, endTime - startTime);
        } catch (ScriptException e) {
            long endTime = System.currentTimeMillis();
            LOG.error("Error executing Kotlin script", e);
            // Generate a unique ID for this specific error event.
            String errorId = UUID.randomUUID().toString();
            LOG.error("Kotlin script execution failed with ID: {}", errorId, e);

            // Create a clean, user-friendly error message. Include the ID.
            String userErrorMessage = String.format(
                    "Error: %s%n(See server error.log for full details. Reference ID: %s)",
                    e.getMessage(), // The simple, human-readable message
                    errorId
            );

            // Return the clean message and any captured output in the result.
            return new ExecutionResult(stringWriter.toString(), userErrorMessage, endTime - startTime);
        } finally {
            printWriter.flush();
            printWriter.close();
        }
    }
}