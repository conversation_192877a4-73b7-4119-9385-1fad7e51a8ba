package com.aemkotlineditor.core.services;

import com.aemkotlineditor.core.exceptions.KotlinScriptingException;
import com.aemkotlineditor.core.models.ExecutionResult;
import org.apache.sling.api.resource.ResourceResolver;
import org.jetbrains.annotations.NotNull;

/**
 * Defines the public contract for a service that executes Kotlin scripts within the AEM environment.
 */
public interface KotlinScriptingService {

    /**
     * Executes a given Kotlin script string.
     *
     * @param resourceResolver The JCR ResourceResolver providing the context for the script execution.
     * This is used to grant the script access to the AEM repository. Must not be null.
     * @param script The raw string containing the Kotlin (.kts) script to be executed. Must not be null.
     * @return A non-null {@link ExecutionResult} object containing the output, error, and timing information.
     */
    ExecutionResult execute(@NotNull ResourceResolver resourceResolver, @NotNull String script) throws KotlinScriptingException;
}
