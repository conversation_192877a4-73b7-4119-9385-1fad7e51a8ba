package com.aemkotlineditor.core.services;

import org.apache.sling.api.resource.ResourceResolver;

/**
 * Simple service interface for executing Kotlin scripts.
 */
public interface SimpleKotlinScriptingService {

    /**
     * Executes a Kotlin script string and returns the result.
     *
     * @param resourceResolver The resource resolver for AEM context
     * @param script The Kotlin script to execute
     * @return The execution result as a string
     */
    String executeScript(ResourceResolver resourceResolver, String script);

    /**
     * Checks if the Kotlin script engine is available.
     *
     * @return true if the engine is ready, false otherwise
     */
    boolean isEngineAvailable();
}
