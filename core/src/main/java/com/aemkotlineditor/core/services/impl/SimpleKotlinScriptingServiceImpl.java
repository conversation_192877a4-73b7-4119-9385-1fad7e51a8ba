package com.aemkotlineditor.core.services.impl;

import com.aemkotlineditor.core.services.SimpleKotlinScriptingService;
import org.apache.sling.api.resource.ResourceResolver;
import org.osgi.service.component.annotations.Activate;
import org.osgi.service.component.annotations.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Simple implementation of Kotlin scripting service for testing OSGi bundle activation.
 */
@Component(service = SimpleKotlinScriptingService.class, immediate = true)
public class SimpleKotlinScriptingServiceImpl implements SimpleKotlinScriptingService {

    private static final Logger LOG = LoggerFactory.getLogger(SimpleKotlinScriptingServiceImpl.class);
    
    private volatile ScriptEngine engine;

    @Activate
    protected void activate() {
        LOG.info("Activating Simple Kotlin Scripting Service...");
        
        ClassLoader currentClassLoader = Thread.currentThread().getContextClassLoader();
        Thread.currentThread().setContextClassLoader(this.getClass().getClassLoader());
        
        try {
            ScriptEngineManager factory = new ScriptEngineManager();
            this.engine = factory.getEngineByName("kotlin");
            
            if (this.engine == null) {
                LOG.error("Kotlin ScriptEngine not found! Available engines:");
                factory.getEngineFactories().forEach(engineFactory -> {
                    LOG.error("  - Engine: {} ({})", engineFactory.getEngineName(), engineFactory.getNames());
                });
            } else {
                LOG.info("Kotlin ScriptEngine loaded successfully: {}", engine.getClass().getName());
            }
        } catch (Exception e) {
            LOG.error("Failed to initialize Kotlin ScriptEngine", e);
        } finally {
            Thread.currentThread().setContextClassLoader(currentClassLoader);
        }
    }

    @Override
    public String executeScript(ResourceResolver resourceResolver, String script) {
        if (engine == null) {
            return "ERROR: Kotlin Script Engine not available";
        }

        StringWriter stringWriter = new StringWriter();
        PrintWriter printWriter = new PrintWriter(stringWriter);

        try {
            SimpleBindings bindings = new SimpleBindings();
            bindings.put("resourceResolver", resourceResolver);
            bindings.put("out", printWriter);

            Object result = engine.eval(script, bindings);
            
            String output = stringWriter.toString();
            if (result != null && !output.contains(result.toString())) {
                output += "\nResult: " + result.toString();
            }
            
            return output.isEmpty() ? "Script executed successfully (no output)" : output;
            
        } catch (ScriptException e) {
            LOG.error("Script execution failed", e);
            return "ERROR: " + e.getMessage();
        } finally {
            printWriter.close();
        }
    }

    @Override
    public boolean isEngineAvailable() {
        return engine != null;
    }
}
