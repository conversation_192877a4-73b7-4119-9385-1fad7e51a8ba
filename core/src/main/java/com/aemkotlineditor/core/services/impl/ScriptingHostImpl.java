package com.aemkotlineditor.core.services.impl;

import com.aemkotlineditor.core.exceptions.KotlinScriptingException;
import com.aemkotlineditor.core.scripts.ResumableScript;
import com.aemkotlineditor.core.services.ScriptingHost;
import com.aemkotlineditor.core.utils.JobConstants;
import com.day.cq.tagging.TagManager;
import com.day.cq.wcm.api.PageManager;
import org.apache.commons.io.IOUtils;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ValueMap;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.osgi.service.component.annotations.Activate;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Deactivate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.script.*;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Implementation of {@link ScriptingHost}.
 * This service handles the complete lifecycle of Kotlin scripts (.kts files):
 * Features:
 * - Script compilation and caching for performance
 * - Full AEM context (ResourceResolver, PageManager, TagManager)
 * - Proper OSGi lifecycle management
 * - Comprehensive error handling and logging
 */
@Component(service = ScriptingHost.class, immediate = true)
public class ScriptingHostImpl implements ScriptingHost {

    private static final Logger LOG = LoggerFactory.getLogger(ScriptingHostImpl.class);

    // Production-ready Kotlin integration using JSR-223 with proper context
    private volatile ScriptEngine kotlinEngine;
    private final Map<String, CompiledScript> compiledScriptCache = new ConcurrentHashMap<>();

    /**
     * Initialize Kotlin ScriptEngine with proper classloader handling for OSGi
     */
    @Activate
    protected void activate() {
        LOG.info("🚀 Activating Production Kotlin ScriptingHost...");

        // Proper classloader switching for OSGi environment
        ClassLoader currentClassLoader = Thread.currentThread().getContextClassLoader();
        Thread.currentThread().setContextClassLoader(this.getClass().getClassLoader());

        try {
            ScriptEngineManager factory = new ScriptEngineManager();
            this.kotlinEngine = factory.getEngineByName("kotlin");

            if (this.kotlinEngine == null) {
                LOG.error("❌ Kotlin ScriptEngine not found! Available engines:");
                factory.getEngineFactories().forEach(engineFactory -> {
                    LOG.error("  - Engine: {} ({})", engineFactory.getEngineName(), engineFactory.getNames());
                });
            } else {
                LOG.info("✅ Production Kotlin ScriptingHost activated successfully!");
                LOG.info("✅ Kotlin ScriptEngine: {}", kotlinEngine.getClass().getName());
            }

        } catch (Exception e) {
            LOG.error("❌ Failed to activate Kotlin ScriptingHost", e);
        } finally {
            Thread.currentThread().setContextClassLoader(currentClassLoader);
        }
    }

    /**
     * Clean up resources on deactivation
     */
    @Deactivate
    protected void deactivate() {
        try {
            // Clear script cache
            compiledScriptCache.clear();
            this.kotlinEngine = null;
        } catch (Exception e) {
            LOG.warn("Error during ScriptingHost cleanup", e);
        }
        LOG.info("Production Kotlin ScriptingHost deactivated");
    }

    /**
     * Compile script using JSR-223 Compilable interface
     */
    @NotNull
    private CompiledScript compileScript(@NotNull String scriptText) throws KotlinScriptingException {
        try {
            if (!(kotlinEngine instanceof Compilable)) {
                throw new KotlinScriptingException("Kotlin engine does not support compilation");
            }

            Compilable compilableEngine = (Compilable) kotlinEngine;
            return compilableEngine.compile(scriptText);

        } catch (ScriptException e) {
            throw new KotlinScriptingException("Failed to compile Kotlin script", e);
        }
    }

    /**
     * Execute compiled script with AEM context as global variables
     */
    @NotNull
    private Object executeScriptWithContext(@NotNull CompiledScript compiledScript, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        try {
            // Create script context with AEM services as global variables
            SimpleScriptContext context = new SimpleScriptContext();
            Bindings bindings = context.getBindings(ScriptContext.ENGINE_SCOPE);

            // Inject AEM context as global variables (standard scripting approach)
            bindings.put("pageManager", resolver.adaptTo(PageManager.class));
            bindings.put("tagManager", resolver.adaptTo(TagManager.class));
            bindings.put("resourceResolver", resolver);
            bindings.put("log", LOG);

            // Execute script with context
            return compiledScript.eval(context);

        } catch (ScriptException e) {
            throw new KotlinScriptingException("Failed to execute compiled script", e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @NotNull
    public Collection<String> collectItems(@NotNull Resource jobResource, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        if (kotlinEngine == null) {
            throw new KotlinScriptingException("Kotlin ScriptEngine not available");
        }

        try {
            LOG.debug("🔍 Collecting items for job: {}", jobResource.getPath());

            // Get ResumableScript instance with AEM context injected as global variables
            ResumableScript scriptInstance = getScriptInstance(jobResource, resolver);
            Map<String, Object> config = getConfig(jobResource);

            // Execute ResumableScript.collectItems method
            Collection<String> items = scriptInstance.collectItems(resolver, config);

            LOG.info("✅ Collected {} items for job: {}", items.size(), jobResource.getPath());
            return items;

        } catch (Exception e) {
            String errorId = UUID.randomUUID().toString();
            LOG.error("❌ Kotlin script item collection failed with ID: {}", errorId, e);
            throw new KotlinScriptingException("Item collection failed: " + e.getMessage(), e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void processItem(@NotNull Resource jobResource, @NotNull ResourceResolver resolver, @NotNull String itemIdentifier) throws KotlinScriptingException {
        if (kotlinEngine == null) {
            throw new KotlinScriptingException("Kotlin ScriptEngine not available");
        }

        try {
            LOG.debug("⚙️ Processing item: {} for job: {}", itemIdentifier, jobResource.getPath());

            // Get ResumableScript instance with AEM context injected as global variables
            ResumableScript scriptInstance = getScriptInstance(jobResource, resolver);
            Map<String, Object> config = getConfig(jobResource);

            // Execute ResumableScript.processItem method
            scriptInstance.processItem(resolver, itemIdentifier, config);

            LOG.debug("✅ Processed item: {} for job: {}", itemIdentifier, jobResource.getPath());

        } catch (Exception e) {
            String errorId = UUID.randomUUID().toString();
            LOG.error("❌ Kotlin script item processing failed with ID: {}", errorId, e);
            throw new KotlinScriptingException("Item processing failed: " + e.getMessage(), e);
        }
    }

    /**
     * Get ResumableScript instance using JSR-223 with AEM context as global variables
     */
    @NotNull
    private ResumableScript getScriptInstance(@NotNull Resource jobResource, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        ValueMap jobProperties = jobResource.getValueMap();
        String scriptPath = jobProperties.get("scriptResourcePath", String.class);
        String scriptSource = jobProperties.get(JobConstants.SCRIPT_SOURCE_PROPERTY, String.class);

        // The cache key is the JCR path, or if not available, the job path itself (for ad-hoc scripts).
        String cacheKey = scriptPath != null ? scriptPath : jobResource.getPath();

        try {
            // Use cached compiled script or compile new one
            CompiledScript compiledScript = compiledScriptCache.computeIfAbsent(cacheKey, key -> {
                try {
                    String scriptText = loadScriptText(key, scriptSource, scriptPath, resolver);
                    return compileScript(scriptText);
                } catch (Exception e) {
                    throw new RuntimeException(new KotlinScriptingException("Failed to load or compile script for job: " + jobResource.getName(), e));
                }
            });

            // Execute script with AEM context as global variables
            Object scriptResult = executeScriptWithContext(compiledScript, resolver);

            // Ensure the script result implements ResumableScript
            if (!(scriptResult instanceof ResumableScript)) {
                throw new KotlinScriptingException("Script for job " + jobResource.getName() + " does not implement ResumableScript interface.");
            }

            return (ResumableScript) scriptResult;

        } catch (RuntimeException re) {
            // Un-wrap the exception from the lambda
            if (re.getCause() instanceof KotlinScriptingException) {
                throw (KotlinScriptingException) re.getCause();
            }
            throw new KotlinScriptingException("An unexpected error occurred while loading script: " + jobResource.getName(), re);
        }
    }

    /**
     * Load script text from JCR or embedded source
     */
    @NotNull
    private String loadScriptText(@NotNull String cacheKey, @Nullable String scriptSource,
                                @Nullable String scriptPath, @NotNull ResourceResolver resolver)
                                throws KotlinScriptingException, IOException {
        if (scriptPath != null) {
            Resource scriptResource = resolver.getResource(scriptPath);
            if (scriptResource == null) throw new KotlinScriptingException("Script resource not found at: " + scriptPath);
            try (InputStream content = scriptResource.adaptTo(InputStream.class)) {
                if (content == null) throw new KotlinScriptingException("Cannot read script content from: " + scriptPath);
                return IOUtils.toString(content, StandardCharsets.UTF_8);
            }
        } else if (scriptSource != null) {
            return scriptSource;
        } else {
            throw new KotlinScriptingException("Job node is missing 'scriptResourcePath' or 'scriptSource' property. Cannot determine script source.");
        }
    }

    /**
     * Extract configuration from job resource
     */
    @NotNull
    private Map<String, Object> getConfig(@NotNull Resource jobResource) {
        Resource configResource = jobResource.getChild("config");
        return configResource != null ? new HashMap<>(configResource.getValueMap()) : Collections.emptyMap();
    }


}