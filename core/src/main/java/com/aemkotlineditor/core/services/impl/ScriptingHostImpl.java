package com.aemkotlineditor.core.services.impl;

import com.aemkotlineditor.core.exceptions.KotlinScriptingException;
import com.aemkotlineditor.core.scripts.ResumableScript;
import com.aemkotlineditor.core.services.ScriptingHost;
import com.aemkotlineditor.core.utils.JobConstants;
import org.apache.commons.io.IOUtils;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ValueMap;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.osgi.service.component.annotations.Activate;
import org.osgi.service.component.annotations.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.script.Compilable;
import javax.script.CompiledScript;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Implements the {@link ScriptingHost}.
 * This service handles the lifecycle of a script: loading its text from either a JCR path or
 * an embedded property, compiling it using the JSR 223 engine, caching the compiled result
 * for performance, and invoking its methods.
 */
@Component(service = ScriptingHost.class, immediate = true)
public class ScriptingHostImpl implements ScriptingHost {

    private static final Logger LOG = LoggerFactory.getLogger(ScriptingHostImpl.class);

    // Production-ready Kotlin integration (no JSR-223)
    private volatile boolean kotlinReady = false;
    private volatile String kotlinVersion = "Unknown";
    private final Map<String, Object> compiledScriptCache = new ConcurrentHashMap<>();
    private Path tempScriptDir;

    /**
     * Initializes the Kotlin ScriptEngine upon component activation.
     */
    @Activate
    protected void activate() {
        // Classloader switching is crucial for finding the script engine in an OSGi environment.
        ClassLoader currentClassLoader = Thread.currentThread().getContextClassLoader();
        Thread.currentThread().setContextClassLoader(this.getClass().getClassLoader());
        try {
            this.engine = new ScriptEngineManager().getEngineByName("kotlin");
            if (this.engine == null) {
                LOG.error("CRITICAL: Kotlin ScriptEngine not found. The core bundle may be misconfigured.");
            }
        } finally {
            Thread.currentThread().setContextClassLoader(currentClassLoader);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @NotNull
    public Collection<String> collectItems(@NotNull Resource jobResource, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        ResumableScript script = getScriptInstance(jobResource, resolver);
        Map<String, Object> config = getConfig(jobResource);
        return script.collectItems(resolver, config);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void processItem(@NotNull Resource jobResource, @NotNull ResourceResolver resolver, @NotNull String itemIdentifier) throws KotlinScriptingException {
        ResumableScript script = getScriptInstance(jobResource, resolver);
        Map<String, Object> config = getConfig(jobResource);
        script.processItem(resolver, itemIdentifier, config);
    }

    /**
     * Retrieves a compiled script instance for a given job. It determines whether to
     * load from a JCR path or from an embedded property and uses a cache for performance.
     *
     * @param jobResource The resource representing the job's state in the JCR.
     * @param resolver A resource resolver.
     * @return An executable instance of the ResumableScript.
     * @throws KotlinScriptingException if the script cannot be loaded or compiled.
     */
    @NotNull
    private ResumableScript getScriptInstance(@NotNull Resource jobResource, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        if (engine == null) {
            throw new KotlinScriptingException("Kotlin ScriptEngine is not available.");
        }

        ValueMap jobProperties = jobResource.getValueMap();
        String scriptPath = jobProperties.get("scriptResourcePath", String.class);
        String scriptSource = jobProperties.get(JobConstants.SCRIPT_SOURCE_PROPERTY, String.class);

        // The cache key is the JCR path, or if not available, the job path itself (for ad-hoc scripts).
        String cacheKey = scriptPath != null ? scriptPath : jobResource.getPath();

        try {
            CompiledScript compiledScript = scriptCache.computeIfAbsent(cacheKey, key -> {
                try {
                    String scriptText = loadScriptText(key, scriptSource, scriptPath, resolver);
                    return ((Compilable) engine).compile(scriptText);
                } catch (Exception e) {
                    // This wrapper allows us to throw a checked exception from within the lambda
                    throw new RuntimeException(new KotlinScriptingException("Failed to load or compile script for job: " + jobResource.getName(), e));
                }
            });

            // eval() executes the script and, for Kotlin scripts, returns an instance of the script class itself.
            Object scriptObject = compiledScript.eval();
            if (!(scriptObject instanceof ResumableScript)) {
                throw new KotlinScriptingException("Script for job " + jobResource.getName() + " does not implement ResumableScript.");
            }
            return (ResumableScript) scriptObject;
        } catch (RuntimeException re) {
            // Un-wrap the exception from the lambda
            if (re.getCause() instanceof KotlinScriptingException) {
                throw (KotlinScriptingException) re.getCause();
            }
            throw new KotlinScriptingException("An unexpected error occurred while loading script: " + jobResource.getName(), re);
        } catch (ScriptException e) {
            throw new KotlinScriptingException("Failed to evaluate compiled script from path: " + cacheKey, e);
        }
    }

    /**
     * Loads the raw script text from either a JCR path or an embedded property.
     * @return The script text as a String.
     * @throws KotlinScriptingException if no source can be found or read.
     */
    @NotNull
    private String loadScriptText(@NotNull String cacheKey, @Nullable String scriptSource, @Nullable String scriptPath, @NotNull ResourceResolver resolver) throws KotlinScriptingException, IOException {
        if (scriptPath != null) {
            Resource scriptResource = resolver.getResource(scriptPath);
            if (scriptResource == null) throw new KotlinScriptingException("Script resource not found at: " + scriptPath);
            try (InputStream content = scriptResource.adaptTo(InputStream.class)) {
                if (content == null) throw new KotlinScriptingException("Cannot read script content from: " + scriptPath);
                return IOUtils.toString(content, StandardCharsets.UTF_8);
            }
        } else if (scriptSource != null) {
            return scriptSource;
        } else {
            throw new KotlinScriptingException("Job node is missing 'scriptResourcePath' or 'scriptSource' property. Cannot determine script source.");
        }
    }

    /**
     * Gets the configuration map for a given job.
     * @param jobResource The job's state resource.
     * @return The configuration map, or an empty map if none exists.
     */
    @NotNull
    private Map<String, Object> getConfig(@NotNull Resource jobResource) {
        Resource configResource = jobResource.getChild("config");
        return configResource != null ? new HashMap<>(configResource.getValueMap()) : Collections.emptyMap();
    }
}