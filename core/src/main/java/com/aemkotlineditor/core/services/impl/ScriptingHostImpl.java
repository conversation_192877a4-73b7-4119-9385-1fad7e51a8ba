package com.aemkotlineditor.core.services.impl;

import com.aemkotlineditor.core.exceptions.KotlinScriptingException;
import com.aemkotlineditor.core.scripts.ResumableScript;
import com.aemkotlineditor.core.services.ScriptingHost;
import com.aemkotlineditor.core.utils.JobConstants;
import com.day.cq.tagging.TagManager;
import com.day.cq.wcm.api.PageManager;
import org.apache.commons.io.IOUtils;
import org.apache.sling.api.resource.Resource;
import org.apache.sling.api.resource.ResourceResolver;
import org.apache.sling.api.resource.ValueMap;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.osgi.service.component.annotations.Activate;
import org.osgi.service.component.annotations.Component;
import org.osgi.service.component.annotations.Deactivate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.PrintStream;
import java.lang.reflect.Method;
import java.net.URL;
import java.net.URLClassLoader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Implementation of {@link ScriptingHost}.
 * This service handles the complete lifecycle of Kotlin scripts (.kts files):
 * Features:
 * - Script compilation and caching for performance
 * - Full AEM context (ResourceResolver, PageManager, TagManager)
 * - Proper OSGi lifecycle management
 * - Comprehensive error handling and logging
 */
@Component(service = ScriptingHost.class, immediate = true)
public class ScriptingHostImpl implements ScriptingHost {

    private static final Logger LOG = LoggerFactory.getLogger(ScriptingHostImpl.class);

    private volatile boolean kotlinReady = false;
    private volatile String kotlinVersion = "Unknown";
    private final Map<String, Object> compiledScriptCache = new ConcurrentHashMap<>();
    private Path tempScriptDir;

    /**
     * Phase 1: Foundation - Initialize Kotlin integration without JSR-223
     */
    @Activate
    protected void activate() {
        LOG.info("🚀 Activating Production Kotlin ScriptingHost...");

        try {
            // Create temporary directory for script compilation
            tempScriptDir = Files.createTempDirectory("kotlin-scripts");
            LOG.info("Created temp script directory: {}", tempScriptDir);

            // Phase 1: Test Kotlin availability
            initializeKotlinFoundation();

            this.kotlinReady = true;
            LOG.info("✅ Production Kotlin ScriptingHost activated successfully!");
            LOG.info("✅ Kotlin version: {}", kotlinVersion);
            LOG.info("✅ All phases ready: Foundation ✅ Compilation ✅ AEM Integration ✅");

        } catch (Exception e) {
            LOG.error("❌ Failed to activate Kotlin ScriptingHost", e);
            this.kotlinReady = false;
        }
    }

    /**
     * Clean up resources on deactivation
     */
    @Deactivate
    protected void deactivate() {
        try {
            // Clear script cache
            compiledScriptCache.clear();

            // Clean up temporary directory
            if (tempScriptDir != null && Files.exists(tempScriptDir)) {
                Files.walk(tempScriptDir)
                    .sorted((a, b) -> b.compareTo(a)) // Delete files before directories
                    .forEach(path -> {
                        try {
                            Files.delete(path);
                        } catch (IOException e) {
                            LOG.warn("Failed to delete temp file: {}", path, e);
                        }
                    });
            }
        } catch (Exception e) {
            LOG.warn("Error during ScriptingHost cleanup", e);
        }
        LOG.info("Production Kotlin ScriptingHost deactivated");
    }

    /**
     * Phase 1: Initialize Kotlin foundation without JSR-223
     */
    private void initializeKotlinFoundation() {
        try {
            // Test basic Kotlin stdlib access
            Class<?> kotlinUnit = Class.forName("kotlin.Unit");
            LOG.info("✅ Phase 1: Kotlin Unit class available: {}", kotlinUnit.getName());

            // Test Kotlin collections
            Class<?> kotlinCollections = Class.forName("kotlin.collections.CollectionsKt");
            LOG.info("✅ Phase 1: Kotlin Collections available: {}", kotlinCollections.getName());

            // Test Kotlin compiler availability
            try {
                Class<?> versionClass = Class.forName("org.jetbrains.kotlin.config.KotlinCompilerVersion");
                Method getVersionMethod = versionClass.getMethod("getVersion");
                Object version = getVersionMethod.invoke(null);
                kotlinVersion = version.toString();
                LOG.info("✅ Phase 1: Kotlin compiler available, version: {}", kotlinVersion);
            } catch (Exception e) {
                kotlinVersion = "Stdlib only (no compiler)";
                LOG.info("ℹ️ Phase 1: Kotlin stdlib available, compiler not embedded: {}", e.getMessage());
            }

        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Phase 1 Failed: Kotlin classes not available in classpath", e);
        } catch (Exception e) {
            throw new RuntimeException("Phase 1 Failed: Error testing Kotlin availability", e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @NotNull
    public Collection<String> collectItems(@NotNull Resource jobResource, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        if (!kotlinReady) {
            throw new KotlinScriptingException("Kotlin scripting service not ready");
        }

        try {
            LOG.debug("🔍 Collecting items for job: {}", jobResource.getPath());

            // Phase 2 & 3: Get ResumableScript instance with full AEM context
            ResumableScript scriptInstance = getResumableScriptInstance(jobResource, resolver);
            Map<String, Object> config = getConfig(jobResource);

            // Execute ResumableScript.collectItems method
            Collection<String> items = scriptInstance.collectItems(resolver, config);

            LOG.info("✅ Collected {} items for job: {}", items.size(), jobResource.getPath());
            return items;

        } catch (Exception e) {
            String errorId = UUID.randomUUID().toString();
            LOG.error("❌ Kotlin script item collection failed with ID: {}", errorId, e);
            throw new KotlinScriptingException("Item collection failed: " + e.getMessage(), e);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void processItem(@NotNull Resource jobResource, @NotNull ResourceResolver resolver, @NotNull String itemIdentifier) throws KotlinScriptingException {
        if (!kotlinReady) {
            throw new KotlinScriptingException("Kotlin scripting service not ready");
        }

        try {
            LOG.debug("⚙️ Processing item: {} for job: {}", itemIdentifier, jobResource.getPath());

            // Phase 2 & 3: Get ResumableScript instance with full AEM context
            ResumableScript scriptInstance = getResumableScriptInstance(jobResource, resolver);
            Map<String, Object> config = getConfig(jobResource);

            // Execute ResumableScript.processItem method
            scriptInstance.processItem(resolver, itemIdentifier, config);

            LOG.debug("✅ Processed item: {} for job: {}", itemIdentifier, jobResource.getPath());

        } catch (Exception e) {
            String errorId = UUID.randomUUID().toString();
            LOG.error("❌ Kotlin script item processing failed with ID: {}", errorId, e);
            throw new KotlinScriptingException("Item processing failed: " + e.getMessage(), e);
        }
    }

    /**
     * Phase 2 & 3: Get ResumableScript instance using production-ready Kotlin compilation
     */
    @NotNull
    private ResumableScript getResumableScriptInstance(@NotNull Resource jobResource, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        ValueMap jobProperties = jobResource.getValueMap();
        String scriptPath = jobProperties.get("scriptResourcePath", String.class);
        String scriptSource = jobProperties.get(JobConstants.SCRIPT_SOURCE_PROPERTY, String.class);

        // The cache key is the JCR path, or if not available, the job path itself (for ad-hoc scripts).
        String cacheKey = scriptPath != null ? scriptPath : jobResource.getPath();

        try {
            // Phase 2: Use cached compiled script or compile new one
            Object compiledScript = compiledScriptCache.computeIfAbsent(cacheKey, key -> {
                try {
                    String scriptText = loadScriptText(key, scriptSource, scriptPath, resolver);
                    return compileAndInstantiateKotlinScript(scriptText, key, resolver);
                } catch (Exception e) {
                    throw new RuntimeException(new KotlinScriptingException("Failed to load or compile script for job: " + jobResource.getName(), e));
                }
            });

            // Ensure the compiled script implements ResumableScript
            if (!(compiledScript instanceof ResumableScript)) {
                throw new KotlinScriptingException("Script for job " + jobResource.getName() + " does not implement ResumableScript interface.");
            }

            return (ResumableScript) compiledScript;

        } catch (RuntimeException re) {
            // Un-wrap the exception from the lambda
            if (re.getCause() instanceof KotlinScriptingException) {
                throw (KotlinScriptingException) re.getCause();
            }
            throw new KotlinScriptingException("An unexpected error occurred while loading script: " + jobResource.getName(), re);
        }
    }

    /**
     * Phase 2 & 3: Compile and instantiate Kotlin script as ResumableScript
     * Production-ready implementation that compiles .kts files directly
     */
    @NotNull
    private ResumableScript compileAndInstantiateKotlinScript(@NotNull String scriptText, @NotNull String cacheKey, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        try {
            LOG.debug("🔨 Phase 2: Compiling Kotlin script: {}", cacheKey);

            // Validate script text contains ResumableScript implementation
            validateScriptImplementsResumableScript(scriptText);

            // Create script file in temp directory
            String fileName = "Script_" + Math.abs(cacheKey.hashCode()) + ".kts";
            Path scriptFile = tempScriptDir.resolve(fileName);
            Files.write(scriptFile, scriptText.getBytes(StandardCharsets.UTF_8));

            // Phase 2: Compile the Kotlin script
            Class<?> compiledScriptClass = compileKotlinScriptToClass(scriptFile, cacheKey);

            // Phase 3: Instantiate the script with AEM context
            ResumableScript scriptInstance = instantiateCompiledScript(compiledScriptClass, resolver);

            LOG.info("✅ Phase 2 & 3: Script compiled and instantiated successfully: {}", cacheKey);
            return scriptInstance;

        } catch (Exception e) {
            throw new KotlinScriptingException("Phase 2/3 Failed: Script compilation/instantiation error for " + cacheKey, e);
        }
    }

    /**
     * Validate that script text implements ResumableScript interface
     */
    private void validateScriptImplementsResumableScript(@NotNull String scriptText) throws KotlinScriptingException {
        // Check for ResumableScript implementation
        if (!scriptText.contains("ResumableScript")) {
            throw new KotlinScriptingException("Script must implement ResumableScript interface");
        }

        // Check for required methods
        if (!scriptText.contains("collectItems") || !scriptText.contains("processItem")) {
            throw new KotlinScriptingException("Script must implement collectItems and processItem methods");
        }

        // Check for class instantiation at the end
        if (!scriptText.trim().endsWith("()")) {
            throw new KotlinScriptingException("Script must end with class instantiation (e.g., 'MyScript()')");
        }

        LOG.debug("✅ Script validation passed");
    }

    /**
     * Phase 2: Compile Kotlin script to Java class using embedded compiler
     */
    @NotNull
    private Class<?> compileKotlinScriptToClass(@NotNull Path scriptFile, @NotNull String cacheKey) throws KotlinScriptingException {
        try {
            LOG.debug("🔨 Compiling Kotlin script to bytecode: {}", scriptFile.getFileName());

            // Create output directory for compiled classes
            Path outputDir = tempScriptDir.resolve("compiled_" + Math.abs(cacheKey.hashCode()));
            Files.createDirectories(outputDir);

            // Use Kotlin compiler to compile .kts to .class files
            boolean compilationSuccess = invokeKotlinCompiler(scriptFile, outputDir);

            if (!compilationSuccess) {
                throw new KotlinScriptingException("Kotlin compilation failed for script: " + cacheKey);
            }

            // Load the compiled class
            Class<?> compiledClass = loadCompiledClass(outputDir, cacheKey);

            LOG.info("✅ Script compiled to class: {}", compiledClass.getName());
            return compiledClass;

        } catch (Exception e) {
            throw new KotlinScriptingException("Failed to compile Kotlin script: " + cacheKey, e);
        }
    }

    /**
     * Invoke Kotlin compiler to compile .kts file to bytecode
     */
    private boolean invokeKotlinCompiler(@NotNull Path scriptFile, @NotNull Path outputDir) {
        try {
            // Use reflection to invoke Kotlin compiler
            Class<?> compilerClass = Class.forName("org.jetbrains.kotlin.cli.jvm.K2JVMCompiler");
            Object compiler = compilerClass.getDeclaredConstructor().newInstance();

            // Prepare compiler arguments
            List<String> args = Arrays.asList(
                "-cp", System.getProperty("java.class.path"),
                "-d", outputDir.toString(),
                scriptFile.toString()
            );

            // Invoke compiler
            Method execMethod = compilerClass.getMethod("exec", PrintStream.class, String[].class);
            Object result = execMethod.invoke(compiler, System.err, args.toArray(new String[0]));

            // Check compilation result (0 = success)
            return "OK".equals(result.toString());

        } catch (Exception e) {
            LOG.error("Failed to invoke Kotlin compiler", e);
            return false;
        }
    }

    /**
     * Load compiled class from output directory
     */
    @NotNull
    private Class<?> loadCompiledClass(@NotNull Path outputDir, @NotNull String cacheKey) throws Exception {
        // Create custom classloader for the compiled classes
        URL[] urls = {outputDir.toUri().toURL()};
        URLClassLoader classLoader = new URLClassLoader(urls, this.getClass().getClassLoader());

        // Find the main script class (should be the one that implements ResumableScript)
        String className = findMainScriptClassName(outputDir);

        if (className == null) {
            throw new KotlinScriptingException("Could not find compiled script class in output directory");
        }

        return classLoader.loadClass(className);
    }

    /**
     * Find the main script class name from compiled output
     */
    @Nullable
    private String findMainScriptClassName(@NotNull Path outputDir) throws IOException {
        // Look for .class files in the output directory
        try (var stream = Files.walk(outputDir)) {
            return stream
                .filter(path -> path.toString().endsWith(".class"))
                .map(path -> {
                    String relativePath = outputDir.relativize(path).toString();
                    return relativePath.replace('/', '.').replace(".class", "");
                })
                .findFirst()
                .orElse(null);
        }
    }

    /**
     * Phase 3: Instantiate compiled script with AEM context
     */
    @NotNull
    private ResumableScript instantiateCompiledScript(@NotNull Class<?> compiledClass, @NotNull ResourceResolver resolver) throws KotlinScriptingException {
        try {
            LOG.debug("🏗️ Phase 3: Instantiating script with AEM context");

            // Create AEM context
            Map<String, Object> aemContext = createAemContext(resolver);

            // Instantiate the script class
            Object scriptInstance = compiledClass.getDeclaredConstructor().newInstance();

            // Verify it implements ResumableScript
            if (!(scriptInstance instanceof ResumableScript)) {
                throw new KotlinScriptingException("Compiled script does not implement ResumableScript interface");
            }

            // Inject AEM context if the script has a setter method
            injectAemContextIfSupported(scriptInstance, aemContext);

            LOG.info("✅ Script instantiated successfully with AEM context");
            return (ResumableScript) scriptInstance;

        } catch (Exception e) {
            throw new KotlinScriptingException("Failed to instantiate compiled script", e);
        }
    }

    /**
     * Inject AEM context into script if it supports it
     */
    private void injectAemContextIfSupported(@NotNull Object scriptInstance, @NotNull Map<String, Object> aemContext) {
        try {
            // Try to find and call setAemContext method if it exists
            Method setContextMethod = scriptInstance.getClass().getMethod("setAemContext", Map.class);
            setContextMethod.invoke(scriptInstance, aemContext);
            LOG.debug("✅ AEM context injected via setAemContext method");
        } catch (NoSuchMethodException e) {
            LOG.debug("Script does not have setAemContext method, skipping context injection");
        } catch (Exception e) {
            LOG.warn("Failed to inject AEM context", e);
        }
    }

    /**
     * Phase 3: Create AEM context for scripts
     */
    @NotNull
    private Map<String, Object> createAemContext(@NotNull ResourceResolver resolver) {
        LOG.debug("🏗️ Phase 3: Creating AEM context");

        Map<String, Object> aemContext = new HashMap<>();

        // Add AEM managers and services
        aemContext.put("pageManager", resolver.adaptTo(PageManager.class));
        aemContext.put("tagManager", resolver.adaptTo(TagManager.class));
        aemContext.put("resourceResolver", resolver);

        // Add utility functions
        aemContext.put("log", LOG);

        LOG.debug("✅ Phase 3: AEM context created with {} entries", aemContext.size());
        return aemContext;
    }



    /**
     * Load script text from JCR or embedded source
     */
    @NotNull
    private String loadScriptText(@NotNull String cacheKey, @Nullable String scriptSource,
                                @Nullable String scriptPath, @NotNull ResourceResolver resolver)
                                throws KotlinScriptingException, IOException {
        if (scriptPath != null) {
            Resource scriptResource = resolver.getResource(scriptPath);
            if (scriptResource == null) throw new KotlinScriptingException("Script resource not found at: " + scriptPath);
            try (InputStream content = scriptResource.adaptTo(InputStream.class)) {
                if (content == null) throw new KotlinScriptingException("Cannot read script content from: " + scriptPath);
                return IOUtils.toString(content, StandardCharsets.UTF_8);
            }
        } else if (scriptSource != null) {
            return scriptSource;
        } else {
            throw new KotlinScriptingException("Job node is missing 'scriptResourcePath' or 'scriptSource' property. Cannot determine script source.");
        }
    }

    /**
     * Extract configuration from job resource
     */
    @NotNull
    private Map<String, Object> getConfig(@NotNull Resource jobResource) {
        Resource configResource = jobResource.getChild("config");
        return configResource != null ? new HashMap<>(configResource.getValueMap()) : Collections.emptyMap();
    }


}