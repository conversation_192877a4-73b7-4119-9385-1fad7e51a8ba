Manifest-Version: 1.0
Created-By: Maven Archiver 3.4.0
Build-Jdk-Spec: 23
Bundle-Category: AEM Kotlin Editor
Bundle-ClassPath: .,lib/kotlin-stdlib.jar,lib/kotlin-compiler-embeddable
 .jar,lib/kotlin-script-runtime.jar
Bundle-Description: Core bundle for AEM Kotlin Editor
Bundle-ManifestVersion: 2
Bundle-Name: AEM Kotlin Editor - Core
Bundle-SymbolicName: aem-kotlin-editor.core
Bundle-Version: 1.0.0.SNAPSHOT
Export-Package: com.aemkotlineditor.core.scripts;uses:="com.aemkotlinedi
 tor.core.exceptions,org.apache.sling.api.resource";version="1.0.0",com.
 aemkotlineditor.core.services;version="1.0";uses:="com.aemkotlineditor.
 core.exceptions,org.apache.sling.api.resource",com.aemkotlineditor.core
 .models;version="1.0";uses:="com.fasterxml.jackson.annotation",com.aemk
 otlineditor.core.exceptions;version="1.0.0"
Import-Package: javax.script,org.slf4j;version="[1.7,2)",com.aemkotlined
 itor.core.exceptions;resolution:=optional,com.aemkotlineditor.core.mode
 ls;resolution:=optional;version="[1.0,2)",com.aemkotlineditor.core.scri
 pts;resolution:=optional,com.aemkotlineditor.core.services;resolution:=
 optional;version="[1.0,2)",com.fasterxml.jackson.annotation;resolution:
 =optional;version="[2.17,3)",com.fasterxml.jackson.databind;resolution:
 =optional;version="[2.17,3)",java.awt;resolution:=optional,java.awt.eve
 nt;resolution:=optional,java.beans;resolution:=optional,java.io;resolut
 ion:=optional,java.lang;resolution:=optional,java.lang.annotation;resol
 ution:=optional,java.lang.invoke;resolution:=optional,java.lang.managem
 ent;resolution:=optional,java.lang.ref;resolution:=optional,java.lang.r
 eflect;resolution:=optional,java.math;resolution:=optional,java.net;res
 olution:=optional,java.nio;resolution:=optional,java.nio.channels;resol
 ution:=optional,java.nio.charset;resolution:=optional,java.nio.file;res
 olution:=optional,java.nio.file.attribute;resolution:=optional,java.nio
 .file.spi;resolution:=optional,java.security;resolution:=optional,java.
 text;resolution:=optional,java.time;resolution:=optional,java.time.form
 at;resolution:=optional,java.util;resolution:=optional,java.util.concur
 rent;resolution:=optional,java.util.concurrent.atomic;resolution:=optio
 nal,java.util.concurrent.locks;resolution:=optional,java.util.function;
 resolution:=optional,java.util.jar;resolution:=optional,java.util.loggi
 ng;resolution:=optional,java.util.regex;resolution:=optional,java.util.
 stream;resolution:=optional,java.util.zip;resolution:=optional,javax.an
 notation.processing;resolution:=optional,javax.lang.model;resolution:=o
 ptional,javax.lang.model.element;resolution:=optional,javax.lang.model.
 type;resolution:=optional,javax.lang.model.util;resolution:=optional,ja
 vax.management;resolution:=optional,javax.servlet;resolution:=optional;
 version="[3.1,4)",javax.swing;resolution:=optional,javax.swing.text;res
 olution:=optional,javax.swing.text.html;resolution:=optional,javax.swin
 g.text.html.parser;resolution:=optional,javax.tools;resolution:=optiona
 l,javax.xml.namespace;resolution:=optional,javax.xml.parsers;resolution
 :=optional,javax.xml.stream;resolution:=optional,org.apache.commons.cod
 ec.digest;resolution:=optional;version="[1.17,2)",org.apache.commons.io
 ;resolution:=optional;version="[1.4,2)",org.apache.sling.api;resolution
 :=optional;version="[2.3,3)",org.apache.sling.api.resource;resolution:=
 optional;version="[2.13,3)",org.apache.sling.api.servlets;resolution:=o
 ptional;version="[2.4,3)",org.apache.sling.event.jobs;resolution:=optio
 nal;version="[2.0,3)",org.apache.sling.event.jobs.consumer;resolution:=
 optional;version="[1.3,2)",org.xml.sax;resolution:=optional,org.xml.sax
 .helpers;resolution:=optional
Private-Package: kotlin,kotlin.annotation,kotlin.collections,kotlin.coll
 ections.builders,kotlin.collections.jdk8,kotlin.collections.unsigned,ko
 tlin.comparisons,kotlin.concurrent,kotlin.concurrent.atomics,kotlin.con
 current.internal,kotlin.contracts,kotlin.coroutines,kotlin.coroutines.c
 ancellation,kotlin.coroutines.intrinsics,kotlin.coroutines.jvm.internal
 ,kotlin.enums,kotlin.experimental,kotlin.internal,kotlin.internal.jdk7,
 kotlin.internal.jdk8,kotlin.io,kotlin.io.encoding,kotlin.io.path,kotlin
 .jdk7,kotlin.js,kotlin.jvm,kotlin.jvm.functions,kotlin.jvm.internal,kot
 lin.jvm.internal.markers,kotlin.jvm.internal.unsafe,kotlin.jvm.jdk8,kot
 lin.jvm.optionals,kotlin.math,kotlin.properties,kotlin.random,kotlin.ra
 ndom.jdk8,kotlin.ranges,kotlin.reflect,kotlin.sequences,kotlin.streams.
 jdk8,kotlin.system,kotlin.text,kotlin.text.jdk8,kotlin.time,kotlin.time
 .jdk8,kotlin.uuid,kotlin.reflect.full,kotlin.reflect.jvm,kotlin.reflect
 .jvm.internal,kotlin.reflect.jvm.internal.calls,kotlin.reflect.jvm.inte
 rnal.impl,kotlin.reflect.jvm.internal.impl.builtins,kotlin.reflect.jvm.
 internal.impl.builtins.functions,kotlin.reflect.jvm.internal.impl.built
 ins.jvm,kotlin.reflect.jvm.internal.impl.descriptors,kotlin.reflect.jvm
 .internal.impl.descriptors.annotations,kotlin.reflect.jvm.internal.impl
 .descriptors.deserialization,kotlin.reflect.jvm.internal.impl.descripto
 rs.impl,kotlin.reflect.jvm.internal.impl.descriptors.java,kotlin.reflec
 t.jvm.internal.impl.descriptors.runtime.components,kotlin.reflect.jvm.i
 nternal.impl.descriptors.runtime.structure,kotlin.reflect.jvm.internal.
 impl.incremental,kotlin.reflect.jvm.internal.impl.incremental.component
 s,kotlin.reflect.jvm.internal.impl.km,kotlin.reflect.jvm.internal.impl.
 km.internal,kotlin.reflect.jvm.internal.impl.km.internal.common,kotlin.
 reflect.jvm.internal.impl.km.internal.extensions,kotlin.reflect.jvm.int
 ernal.impl.km.jvm,kotlin.reflect.jvm.internal.impl.km.jvm.internal,kotl
 in.reflect.jvm.internal.impl.load.java,kotlin.reflect.jvm.internal.impl
 .load.java.components,kotlin.reflect.jvm.internal.impl.load.java.descri
 ptors,kotlin.reflect.jvm.internal.impl.load.java.lazy,kotlin.reflect.jv
 m.internal.impl.load.java.lazy.descriptors,kotlin.reflect.jvm.internal.
 impl.load.java.lazy.types,kotlin.reflect.jvm.internal.impl.load.java.so
 urces,kotlin.reflect.jvm.internal.impl.load.java.structure,kotlin.refle
 ct.jvm.internal.impl.load.java.typeEnhancement,kotlin.reflect.jvm.inter
 nal.impl.load.kotlin,kotlin.reflect.jvm.internal.impl.load.kotlin.heade
 r,kotlin.reflect.jvm.internal.impl.metadata,kotlin.reflect.jvm.internal
 .impl.metadata.builtins,kotlin.reflect.jvm.internal.impl.metadata.deser
 ialization,kotlin.reflect.jvm.internal.impl.metadata.jvm,kotlin.reflect
 .jvm.internal.impl.metadata.jvm.deserialization,kotlin.reflect.jvm.inte
 rnal.impl.metadata.serialization,kotlin.reflect.jvm.internal.impl.name,
 kotlin.reflect.jvm.internal.impl.platform,kotlin.reflect.jvm.internal.i
 mpl.protobuf,kotlin.reflect.jvm.internal.impl.renderer,kotlin.reflect.j
 vm.internal.impl.resolve,kotlin.reflect.jvm.internal.impl.resolve.calls
 .inference,kotlin.reflect.jvm.internal.impl.resolve.constants,kotlin.re
 flect.jvm.internal.impl.resolve.deprecation,kotlin.reflect.jvm.internal
 .impl.resolve.descriptorUtil,kotlin.reflect.jvm.internal.impl.resolve.j
 vm,kotlin.reflect.jvm.internal.impl.resolve.sam,kotlin.reflect.jvm.inte
 rnal.impl.resolve.scopes,kotlin.reflect.jvm.internal.impl.resolve.scope
 s.receivers,kotlin.reflect.jvm.internal.impl.serialization,kotlin.refle
 ct.jvm.internal.impl.serialization.deserialization,kotlin.reflect.jvm.i
 nternal.impl.serialization.deserialization.builtins,kotlin.reflect.jvm.
 internal.impl.serialization.deserialization.descriptors,kotlin.reflect.
 jvm.internal.impl.storage,kotlin.reflect.jvm.internal.impl.types,kotlin
 .reflect.jvm.internal.impl.types.checker,kotlin.reflect.jvm.internal.im
 pl.types.error,kotlin.reflect.jvm.internal.impl.types.extensions,kotlin
 .reflect.jvm.internal.impl.types.model,kotlin.reflect.jvm.internal.impl
 .types.typeUtil,kotlin.reflect.jvm.internal.impl.types.typesApproximati
 on,kotlin.reflect.jvm.internal.impl.util,kotlin.reflect.jvm.internal.im
 pl.util.capitalizeDecapitalize,kotlin.reflect.jvm.internal.impl.util.co
 llectionUtils,kotlin.reflect.jvm.internal.impl.utils,kotlin.reflect.jvm
 .internal.impl.utils.addToStdlib,kotlin.script.dependencies,kotlin.scri
 pt.experimental.dependencies,kotlin.script.experimental.location,kotlin
 .script.extensions,kotlin.script.templates,kotlin.script.templates.stan
 dard,org.jetbrains.kotlin,org.jetbrains.kotlin.analysis.decompiled.ligh
 t.classes,org.jetbrains.kotlin.analysis.decompiled.light.classes.origin
 ,org.jetbrains.kotlin.analysis.decompiler.js,org.jetbrains.kotlin.analy
 sis.decompiler.konan,org.jetbrains.kotlin.analysis.decompiler.psi,org.j
 etbrains.kotlin.analysis.decompiler.psi.file,org.jetbrains.kotlin.analy
 sis.decompiler.psi.text,org.jetbrains.kotlin.analysis.decompiler.stub,o
 rg.jetbrains.kotlin.analysis.decompiler.stub.file,org.jetbrains.kotlin.
 analysis.decompiler.stub.flags,org.jetbrains.kotlin.analyzer,org.jetbra
 ins.kotlin.analyzer.common,org.jetbrains.kotlin.asJava,org.jetbrains.ko
 tlin.asJava.builder,org.jetbrains.kotlin.asJava.classes,org.jetbrains.k
 otlin.asJava.elements,org.jetbrains.kotlin.asJava.finder,org.jetbrains.
 kotlin.backend.common,org.jetbrains.kotlin.backend.common.actualizer,or
 g.jetbrains.kotlin.backend.common.actualizer.checker,org.jetbrains.kotl
 in.backend.common.bridges,org.jetbrains.kotlin.backend.common.checkers,
 org.jetbrains.kotlin.backend.common.checkers.context,org.jetbrains.kotl
 in.backend.common.checkers.declaration,org.jetbrains.kotlin.backend.com
 mon.checkers.expression,org.jetbrains.kotlin.backend.common.checkers.ty
 pe,org.jetbrains.kotlin.backend.common.descriptors,org.jetbrains.kotlin
 .backend.common.diagnostics,org.jetbrains.kotlin.backend.common.extensi
 ons,org.jetbrains.kotlin.backend.common.ir,org.jetbrains.kotlin.backend
 .common.linkage,org.jetbrains.kotlin.backend.common.linkage.issues,org.
 jetbrains.kotlin.backend.common.linkage.partial,org.jetbrains.kotlin.ba
 ckend.common.lower,org.jetbrains.kotlin.backend.common.lower.coroutines
 ,org.jetbrains.kotlin.backend.common.lower.inline,org.jetbrains.kotlin.
 backend.common.lower.loops,org.jetbrains.kotlin.backend.common.lower.lo
 ops.handlers,org.jetbrains.kotlin.backend.common.lower.optimizations,or
 g.jetbrains.kotlin.backend.common.output,org.jetbrains.kotlin.backend.c
 ommon.overrides,org.jetbrains.kotlin.backend.common.phaser,org.jetbrain
 s.kotlin.backend.common.serialization,org.jetbrains.kotlin.backend.comm
 on.serialization.encodings,org.jetbrains.kotlin.backend.common.serializ
 ation.mangle,org.jetbrains.kotlin.backend.common.serialization.mangle.d
 escriptor,org.jetbrains.kotlin.backend.common.serialization.mangle.ir,o
 rg.jetbrains.kotlin.backend.common.serialization.metadata,org.jetbrains
 .kotlin.backend.common.serialization.proto,org.jetbrains.kotlin.backend
 .common.serialization.signature,org.jetbrains.kotlin.backend.js,org.jet
 brains.kotlin.backend.jvm,org.jetbrains.kotlin.backend.jvm.caches,org.j
 etbrains.kotlin.backend.jvm.codegen,org.jetbrains.kotlin.backend.jvm.ex
 tensions,org.jetbrains.kotlin.backend.jvm.intrinsics,org.jetbrains.kotl
 in.backend.jvm.ir,org.jetbrains.kotlin.backend.jvm.lower,org.jetbrains.
 kotlin.backend.jvm.lower.indy,org.jetbrains.kotlin.backend.jvm.mapping,
 org.jetbrains.kotlin.backend.jvm.metadata,org.jetbrains.kotlin.backend.
 jvm.overrides,org.jetbrains.kotlin.backend.jvm.serialization,org.jetbra
 ins.kotlin.backend.jvm.serialization.proto,org.jetbrains.kotlin.backend
 .konan,org.jetbrains.kotlin.backend.konan.descriptors,org.jetbrains.kot
 lin.backend.konan.ir,org.jetbrains.kotlin.backend.konan.lower,org.jetbr
 ains.kotlin.backend.konan.serialization,org.jetbrains.kotlin.backend.wa
 sm,org.jetbrains.kotlin.backend.wasm.dce,org.jetbrains.kotlin.backend.w
 asm.dwarf,org.jetbrains.kotlin.backend.wasm.dwarf.entries,org.jetbrains
 .kotlin.backend.wasm.dwarf.lines,org.jetbrains.kotlin.backend.wasm.dwar
 f.utils,org.jetbrains.kotlin.backend.wasm.export,org.jetbrains.kotlin.b
 ackend.wasm.ic,org.jetbrains.kotlin.backend.wasm.ir2wasm,org.jetbrains.
 kotlin.backend.wasm.lower,org.jetbrains.kotlin.backend.wasm.serializati
 on,org.jetbrains.kotlin.backend.wasm.utils,org.jetbrains.kotlin.build,o
 rg.jetbrains.kotlin.build.report,org.jetbrains.kotlin.build.report.metr
 ics,org.jetbrains.kotlin.build.report.statistics,org.jetbrains.kotlin.b
 uild.report.statistics.file,org.jetbrains.kotlin.buildtools.api,org.jet
 brains.kotlin.buildtools.api.internal,org.jetbrains.kotlin.buildtools.a
 pi.internal.wrappers,org.jetbrains.kotlin.buildtools.api.jvm,org.jetbra
 ins.kotlin.buildtools.internal,org.jetbrains.kotlin.builtins,org.jetbra
 ins.kotlin.builtins.functions,org.jetbrains.kotlin.builtins.jvm,org.jet
 brains.kotlin.builtins.konan,org.jetbrains.kotlin.cfg,org.jetbrains.kot
 lin.cfg.pseudocode,org.jetbrains.kotlin.cfg.pseudocode.instructions,org
 .jetbrains.kotlin.cfg.pseudocode.instructions.eval,org.jetbrains.kotlin
 .cfg.pseudocode.instructions.jumps,org.jetbrains.kotlin.cfg.pseudocode.
 instructions.special,org.jetbrains.kotlin.cfg.pseudocodeTraverser,org.j
 etbrains.kotlin.cfg.variable,org.jetbrains.kotlin.checkers,org.jetbrain
 s.kotlin.checkers.diagnostics,org.jetbrains.kotlin.checkers.diagnostics
 .factories,org.jetbrains.kotlin.checkers.utils,org.jetbrains.kotlin.cli
 .common,org.jetbrains.kotlin.cli.common.arguments,org.jetbrains.kotlin.
 cli.common.config,org.jetbrains.kotlin.cli.common.environment,org.jetbr
 ains.kotlin.cli.common.extensions,org.jetbrains.kotlin.cli.common.fir,o
 rg.jetbrains.kotlin.cli.common.localfs,org.jetbrains.kotlin.cli.common.
 messages,org.jetbrains.kotlin.cli.common.modules,org.jetbrains.kotlin.c
 li.common.output,org.jetbrains.kotlin.cli.common.profiling,org.jetbrain
 s.kotlin.cli.common.repl,org.jetbrains.kotlin.cli.js,org.jetbrains.kotl
 in.cli.js.klib,org.jetbrains.kotlin.cli.jvm,org.jetbrains.kotlin.cli.jv
 m.compiler,org.jetbrains.kotlin.cli.jvm.compiler.jarfs,org.jetbrains.ko
 tlin.cli.jvm.compiler.legacy.pipeline,org.jetbrains.kotlin.cli.jvm.conf
 ig,org.jetbrains.kotlin.cli.jvm.index,org.jetbrains.kotlin.cli.jvm.java
 c,org.jetbrains.kotlin.cli.jvm.modules,org.jetbrains.kotlin.cli.jvm.plu
 gins,org.jetbrains.kotlin.cli.metadata,org.jetbrains.kotlin.cli.pipelin
 e,org.jetbrains.kotlin.cli.pipeline.jvm,org.jetbrains.kotlin.cli.pipeli
 ne.metadata,org.jetbrains.kotlin.cli.pipeline.web,org.jetbrains.kotlin.
 cli.pipeline.web.js,org.jetbrains.kotlin.cli.pipeline.web.wasm,org.jetb
 rains.kotlin.cli.plugins,org.jetbrains.kotlin.codegen,org.jetbrains.kot
 lin.codegen.coroutines,org.jetbrains.kotlin.codegen.extensions,org.jetb
 rains.kotlin.codegen.inline,org.jetbrains.kotlin.codegen.inline.corouti
 nes,org.jetbrains.kotlin.codegen.intrinsics,org.jetbrains.kotlin.codege
 n.optimization,org.jetbrains.kotlin.codegen.optimization.boxing,org.jet
 brains.kotlin.codegen.optimization.common,org.jetbrains.kotlin.codegen.
 optimization.fixStack,org.jetbrains.kotlin.codegen.optimization.nullChe
 ck,org.jetbrains.kotlin.codegen.optimization.temporaryVals,org.jetbrain
 s.kotlin.codegen.optimization.transformer,org.jetbrains.kotlin.codegen.
 pseudoInsns,org.jetbrains.kotlin.codegen.serialization,org.jetbrains.ko
 tlin.codegen.signature,org.jetbrains.kotlin.codegen.state,org.jetbrains
 .kotlin.codegen.when,org.jetbrains.kotlin.com.fasterxml.aalto,org.jetbr
 ains.kotlin.com.fasterxml.aalto.impl,org.jetbrains.kotlin.com.fasterxml
 .aalto.in,org.jetbrains.kotlin.com.fasterxml.aalto.stax,org.jetbrains.k
 otlin.com.fasterxml.aalto.util,org.jetbrains.kotlin.com.google.common.b
 ase,org.jetbrains.kotlin.com.google.common.collect,org.jetbrains.kotlin
 .com.google.common.graph,org.jetbrains.kotlin.com.google.common.hash,or
 g.jetbrains.kotlin.com.google.common.io,org.jetbrains.kotlin.com.google
 .common.math,org.jetbrains.kotlin.com.google.common.primitives,org.jetb
 rains.kotlin.com.google.common.util.concurrent,org.jetbrains.kotlin.com
 .google.gwt.dev.js,org.jetbrains.kotlin.com.google.gwt.dev.js.parserExc
 eptions,org.jetbrains.kotlin.com.google.gwt.dev.js.rhino,org.jetbrains.
 kotlin.com.intellij,org.jetbrains.kotlin.com.intellij.codeInsight,org.j
 etbrains.kotlin.com.intellij.codeInsight.completion.scope,org.jetbrains
 .kotlin.com.intellij.codeInsight.daemon.impl.analysis,org.jetbrains.kot
 lin.com.intellij.codeInsight.folding,org.jetbrains.kotlin.com.intellij.
 codeInsight.folding.impl,org.jetbrains.kotlin.com.intellij.codeInsight.
 highlighting,org.jetbrains.kotlin.com.intellij.codeInsight.javadoc,org.
 jetbrains.kotlin.com.intellij.codeInsight.runner,org.jetbrains.kotlin.c
 om.intellij.codeWithMe,org.jetbrains.kotlin.com.intellij.concurrency,or
 g.jetbrains.kotlin.com.intellij.core,org.jetbrains.kotlin.com.intellij.
 diagnostic,org.jetbrains.kotlin.com.intellij.extapi.psi,org.jetbrains.k
 otlin.com.intellij.formatting,org.jetbrains.kotlin.com.intellij.ide,org
 .jetbrains.kotlin.com.intellij.ide.highlighter,org.jetbrains.kotlin.com
 .intellij.ide.plugins,org.jetbrains.kotlin.com.intellij.ide.plugins.cl,
 org.jetbrains.kotlin.com.intellij.ide.util,org.jetbrains.kotlin.com.int
 ellij.idea,org.jetbrains.kotlin.com.intellij.injected.editor,org.jetbra
 ins.kotlin.com.intellij.java.frontback.psi.fake,org.jetbrains.kotlin.co
 m.intellij.java.frontback.psi.impl.fake,org.jetbrains.kotlin.com.intell
 ij.lang,org.jetbrains.kotlin.com.intellij.lang.folding,org.jetbrains.ko
 tlin.com.intellij.lang.impl,org.jetbrains.kotlin.com.intellij.lang.inje
 ction,org.jetbrains.kotlin.com.intellij.lang.java,org.jetbrains.kotlin.
 com.intellij.lang.java.beans,org.jetbrains.kotlin.com.intellij.lang.jav
 a.lexer,org.jetbrains.kotlin.com.intellij.lang.java.parser,org.jetbrain
 s.kotlin.com.intellij.lang.jvm,org.jetbrains.kotlin.com.intellij.lang.j
 vm.annotation,org.jetbrains.kotlin.com.intellij.lang.jvm.facade,org.jet
 brains.kotlin.com.intellij.lang.jvm.types,org.jetbrains.kotlin.com.inte
 llij.lexer,org.jetbrains.kotlin.com.intellij.mock,org.jetbrains.kotlin.
 com.intellij.model,org.jetbrains.kotlin.com.intellij.model.psi,org.jetb
 rains.kotlin.com.intellij.navigation,org.jetbrains.kotlin.com.intellij.
 notebook.editor,org.jetbrains.kotlin.com.intellij.openapi,org.jetbrains
 .kotlin.com.intellij.openapi.application,org.jetbrains.kotlin.com.intel
 lij.openapi.application.ex,org.jetbrains.kotlin.com.intellij.openapi.ap
 plication.impl,org.jetbrains.kotlin.com.intellij.openapi.client,org.jet
 brains.kotlin.com.intellij.openapi.command,org.jetbrains.kotlin.com.int
 ellij.openapi.command.impl,org.jetbrains.kotlin.com.intellij.openapi.co
 mmand.undo,org.jetbrains.kotlin.com.intellij.openapi.components,org.jet
 brains.kotlin.com.intellij.openapi.diagnostic,org.jetbrains.kotlin.com.
 intellij.openapi.editor,org.jetbrains.kotlin.com.intellij.openapi.edito
 r.actionSystem,org.jetbrains.kotlin.com.intellij.openapi.editor.colors,
 org.jetbrains.kotlin.com.intellij.openapi.editor.event,org.jetbrains.ko
 tlin.com.intellij.openapi.editor.ex,org.jetbrains.kotlin.com.intellij.o
 penapi.editor.impl,org.jetbrains.kotlin.com.intellij.openapi.editor.imp
 l.event,org.jetbrains.kotlin.com.intellij.openapi.editor.markup,org.jet
 brains.kotlin.com.intellij.openapi.extensions,org.jetbrains.kotlin.com.
 intellij.openapi.extensions.impl,org.jetbrains.kotlin.com.intellij.open
 api.fileEditor,org.jetbrains.kotlin.com.intellij.openapi.fileEditor.imp
 l,org.jetbrains.kotlin.com.intellij.openapi.fileTypes,org.jetbrains.kot
 lin.com.intellij.openapi.module,org.jetbrains.kotlin.com.intellij.opena
 pi.progress,org.jetbrains.kotlin.com.intellij.openapi.progress.impl,org
 .jetbrains.kotlin.com.intellij.openapi.progress.util,org.jetbrains.kotl
 in.com.intellij.openapi.project,org.jetbrains.kotlin.com.intellij.opena
 pi.projectRoots,org.jetbrains.kotlin.com.intellij.openapi.roots,org.jet
 brains.kotlin.com.intellij.openapi.roots.impl,org.jetbrains.kotlin.com.
 intellij.openapi.ui,org.jetbrains.kotlin.com.intellij.openapi.util,org.
 jetbrains.kotlin.com.intellij.openapi.util.io,org.jetbrains.kotlin.com.
 intellij.openapi.util.objectTree,org.jetbrains.kotlin.com.intellij.open
 api.util.registry,org.jetbrains.kotlin.com.intellij.openapi.util.text,o
 rg.jetbrains.kotlin.com.intellij.openapi.vfs,org.jetbrains.kotlin.com.i
 ntellij.openapi.vfs.encoding,org.jetbrains.kotlin.com.intellij.openapi.
 vfs.impl,org.jetbrains.kotlin.com.intellij.openapi.vfs.impl.jar,org.jet
 brains.kotlin.com.intellij.openapi.vfs.local,org.jetbrains.kotlin.com.i
 ntellij.openapi.vfs.newvfs,org.jetbrains.kotlin.com.intellij.openapi.vf
 s.newvfs.events,org.jetbrains.kotlin.com.intellij.openapi.vfs.pointers,
 org.jetbrains.kotlin.com.intellij.openapi.wm.ex,org.jetbrains.kotlin.co
 m.intellij.patterns,org.jetbrains.kotlin.com.intellij.patterns.compiler
 ,org.jetbrains.kotlin.com.intellij.platform.backend.navigation,org.jetb
 rains.kotlin.com.intellij.platform.diagnostic.telemetry,org.jetbrains.k
 otlin.com.intellij.platform.diagnostic.telemetry.helpers,org.jetbrains.
 kotlin.com.intellij.pom,org.jetbrains.kotlin.com.intellij.pom.core.impl
 ,org.jetbrains.kotlin.com.intellij.pom.event,org.jetbrains.kotlin.com.i
 ntellij.pom.impl,org.jetbrains.kotlin.com.intellij.pom.java,org.jetbrai
 ns.kotlin.com.intellij.pom.tree,org.jetbrains.kotlin.com.intellij.pom.t
 ree.events,org.jetbrains.kotlin.com.intellij.pom.tree.events.impl,org.j
 etbrains.kotlin.com.intellij.pom.wrappers,org.jetbrains.kotlin.com.inte
 llij.psi,org.jetbrains.kotlin.com.intellij.psi.augment,org.jetbrains.ko
 tlin.com.intellij.psi.codeStyle,org.jetbrains.kotlin.com.intellij.psi.c
 ompiled,org.jetbrains.kotlin.com.intellij.psi.controlFlow,org.jetbrains
 .kotlin.com.intellij.psi.css,org.jetbrains.kotlin.com.intellij.psi.filt
 ers,org.jetbrains.kotlin.com.intellij.psi.filters.classes,org.jetbrains
 .kotlin.com.intellij.psi.filters.element,org.jetbrains.kotlin.com.intel
 lij.psi.filters.position,org.jetbrains.kotlin.com.intellij.psi.impl,org
 .jetbrains.kotlin.com.intellij.psi.impl.cache,org.jetbrains.kotlin.com.
 intellij.psi.impl.compiled,org.jetbrains.kotlin.com.intellij.psi.impl.f
 ile,org.jetbrains.kotlin.com.intellij.psi.impl.file.impl,org.jetbrains.
 kotlin.com.intellij.psi.impl.java.stubs,org.jetbrains.kotlin.com.intell
 ij.psi.impl.java.stubs.impl,org.jetbrains.kotlin.com.intellij.psi.impl.
 java.stubs.index,org.jetbrains.kotlin.com.intellij.psi.impl.light,org.j
 etbrains.kotlin.com.intellij.psi.impl.meta,org.jetbrains.kotlin.com.int
 ellij.psi.impl.search,org.jetbrains.kotlin.com.intellij.psi.impl.smartP
 ointers,org.jetbrains.kotlin.com.intellij.psi.impl.source,org.jetbrains
 .kotlin.com.intellij.psi.impl.source.codeStyle,org.jetbrains.kotlin.com
 .intellij.psi.impl.source.javadoc,org.jetbrains.kotlin.com.intellij.psi
 .impl.source.resolve,org.jetbrains.kotlin.com.intellij.psi.impl.source.
 resolve.graphInference,org.jetbrains.kotlin.com.intellij.psi.impl.sourc
 e.resolve.graphInference.constraints,org.jetbrains.kotlin.com.intellij.
 psi.impl.source.resolve.reference,org.jetbrains.kotlin.com.intellij.psi
 .impl.source.resolve.reference.impl,org.jetbrains.kotlin.com.intellij.p
 si.impl.source.resolve.reference.impl.manipulators,org.jetbrains.kotlin
 .com.intellij.psi.impl.source.resolve.reference.impl.providers,org.jetb
 rains.kotlin.com.intellij.psi.impl.source.tree,org.jetbrains.kotlin.com
 .intellij.psi.impl.source.tree.injected,org.jetbrains.kotlin.com.intell
 ij.psi.impl.source.tree.java,org.jetbrains.kotlin.com.intellij.psi.info
 s,org.jetbrains.kotlin.com.intellij.psi.javadoc,org.jetbrains.kotlin.co
 m.intellij.psi.meta,org.jetbrains.kotlin.com.intellij.psi.presentation.
 java,org.jetbrains.kotlin.com.intellij.psi.scope,org.jetbrains.kotlin.c
 om.intellij.psi.scope.conflictResolvers,org.jetbrains.kotlin.com.intell
 ij.psi.scope.processor,org.jetbrains.kotlin.com.intellij.psi.scope.util
 ,org.jetbrains.kotlin.com.intellij.psi.search,org.jetbrains.kotlin.com.
 intellij.psi.search.impl,org.jetbrains.kotlin.com.intellij.psi.search.s
 earches,org.jetbrains.kotlin.com.intellij.psi.stub,org.jetbrains.kotlin
 .com.intellij.psi.stubs,org.jetbrains.kotlin.com.intellij.psi.targets,o
 rg.jetbrains.kotlin.com.intellij.psi.templateLanguages,org.jetbrains.ko
 tlin.com.intellij.psi.text,org.jetbrains.kotlin.com.intellij.psi.tree,o
 rg.jetbrains.kotlin.com.intellij.psi.tree.java,org.jetbrains.kotlin.com
 .intellij.psi.util,org.jetbrains.kotlin.com.intellij.reference,org.jetb
 rains.kotlin.com.intellij.serialization,org.jetbrains.kotlin.com.intell
 ij.serviceContainer,org.jetbrains.kotlin.com.intellij.testFramework,org
 .jetbrains.kotlin.com.intellij.ui,org.jetbrains.kotlin.com.intellij.ui.
 icons,org.jetbrains.kotlin.com.intellij.util,org.jetbrains.kotlin.com.i
 ntellij.util.cls,org.jetbrains.kotlin.com.intellij.util.codeInsight,org
 .jetbrains.kotlin.com.intellij.util.concurrency,org.jetbrains.kotlin.co
 m.intellij.util.containers,org.jetbrains.kotlin.com.intellij.util.conta
 iners.hash,org.jetbrains.kotlin.com.intellij.util.diff,org.jetbrains.ko
 tlin.com.intellij.util.execution,org.jetbrains.kotlin.com.intellij.util
 .graph,org.jetbrains.kotlin.com.intellij.util.graph.impl,org.jetbrains.
 kotlin.com.intellij.util.indexing,org.jetbrains.kotlin.com.intellij.uti
 l.indexing.impl,org.jetbrains.kotlin.com.intellij.util.io,org.jetbrains
 .kotlin.com.intellij.util.io.keyStorage,org.jetbrains.kotlin.com.intell
 ij.util.io.pagecache,org.jetbrains.kotlin.com.intellij.util.io.pagecach
 e.impl,org.jetbrains.kotlin.com.intellij.util.io.stats,org.jetbrains.ko
 tlin.com.intellij.util.io.storage,org.jetbrains.kotlin.com.intellij.uti
 l.keyFMap,org.jetbrains.kotlin.com.intellij.util.lang,org.jetbrains.kot
 lin.com.intellij.util.messages,org.jetbrains.kotlin.com.intellij.util.m
 essages.impl,org.jetbrains.kotlin.com.intellij.util.pico,org.jetbrains.
 kotlin.com.intellij.util.ref,org.jetbrains.kotlin.com.intellij.util.sys
 tem,org.jetbrains.kotlin.com.intellij.util.text,org.jetbrains.kotlin.co
 m.intellij.util.ui,org.jetbrains.kotlin.com.intellij.util.xml.dom,org.j
 etbrains.kotlin.com.intellij.util.xmlb,org.jetbrains.kotlin.com.intelli
 j.util.xmlb.annotations,org.jetbrains.kotlin.compiler.plugin,org.jetbra
 ins.kotlin.compilerRunner,org.jetbrains.kotlin.config,org.jetbrains.kot
 lin.config.phaser,org.jetbrains.kotlin.constant,org.jetbrains.kotlin.co
 ntainer,org.jetbrains.kotlin.context,org.jetbrains.kotlin.contracts,org
 .jetbrains.kotlin.contracts.description,org.jetbrains.kotlin.contracts.
 description.expressions,org.jetbrains.kotlin.contracts.interpretation,o
 rg.jetbrains.kotlin.contracts.model,org.jetbrains.kotlin.contracts.mode
 l.functors,org.jetbrains.kotlin.contracts.model.structure,org.jetbrains
 .kotlin.contracts.model.visitors,org.jetbrains.kotlin.contracts.parsing
 ,org.jetbrains.kotlin.contracts.parsing.effects,org.jetbrains.kotlin.co
 routines,org.jetbrains.kotlin.descriptors,org.jetbrains.kotlin.descript
 ors.annotations,org.jetbrains.kotlin.descriptors.deserialization,org.je
 tbrains.kotlin.descriptors.impl,org.jetbrains.kotlin.descriptors.java,o
 rg.jetbrains.kotlin.descriptors.konan,org.jetbrains.kotlin.descriptors.
 runtime.components,org.jetbrains.kotlin.descriptors.runtime.structure,o
 rg.jetbrains.kotlin.descriptors.synthetic,org.jetbrains.kotlin.diagnost
 ics,org.jetbrains.kotlin.diagnostics.impl,org.jetbrains.kotlin.diagnost
 ics.rendering,org.jetbrains.kotlin.extensions,org.jetbrains.kotlin.exte
 nsions.internal,org.jetbrains.kotlin.fileClasses,org.jetbrains.kotlin.f
 ir,org.jetbrains.kotlin.fir.analysis,org.jetbrains.kotlin.fir.analysis.
 cfa,org.jetbrains.kotlin.fir.analysis.cfa.util,org.jetbrains.kotlin.fir
 .analysis.checkers,org.jetbrains.kotlin.fir.analysis.checkers.cfa,org.j
 etbrains.kotlin.fir.analysis.checkers.config,org.jetbrains.kotlin.fir.a
 nalysis.checkers.context,org.jetbrains.kotlin.fir.analysis.checkers.dec
 laration,org.jetbrains.kotlin.fir.analysis.checkers.experimental,org.je
 tbrains.kotlin.fir.analysis.checkers.expression,org.jetbrains.kotlin.fi
 r.analysis.checkers.extra,org.jetbrains.kotlin.fir.analysis.checkers.sy
 ntax,org.jetbrains.kotlin.fir.analysis.checkers.type,org.jetbrains.kotl
 in.fir.analysis.collectors,org.jetbrains.kotlin.fir.analysis.collectors
 .components,org.jetbrains.kotlin.fir.analysis.diagnostics,org.jetbrains
 .kotlin.fir.analysis.diagnostics.js,org.jetbrains.kotlin.fir.analysis.d
 iagnostics.jvm,org.jetbrains.kotlin.fir.analysis.diagnostics.native,org
 .jetbrains.kotlin.fir.analysis.diagnostics.wasm,org.jetbrains.kotlin.fi
 r.analysis.diagnostics.web.common,org.jetbrains.kotlin.fir.analysis.ext
 ensions,org.jetbrains.kotlin.fir.analysis.js.checkers,org.jetbrains.kot
 lin.fir.analysis.js.checkers.declaration,org.jetbrains.kotlin.fir.analy
 sis.js.checkers.expression,org.jetbrains.kotlin.fir.analysis.jvm,org.je
 tbrains.kotlin.fir.analysis.jvm.checkers,org.jetbrains.kotlin.fir.analy
 sis.jvm.checkers.declaration,org.jetbrains.kotlin.fir.analysis.jvm.chec
 kers.expression,org.jetbrains.kotlin.fir.analysis.jvm.checkers.type,org
 .jetbrains.kotlin.fir.analysis.native.checkers,org.jetbrains.kotlin.fir
 .analysis.wasm.checkers,org.jetbrains.kotlin.fir.analysis.wasm.checkers
 .declaration,org.jetbrains.kotlin.fir.analysis.wasm.checkers.expression
 ,org.jetbrains.kotlin.fir.analysis.web.common.checkers,org.jetbrains.ko
 tlin.fir.analysis.web.common.checkers.declaration,org.jetbrains.kotlin.
 fir.analysis.web.common.checkers.expression,org.jetbrains.kotlin.fir.ba
 ckend,org.jetbrains.kotlin.fir.backend.generators,org.jetbrains.kotlin.
 fir.backend.jvm,org.jetbrains.kotlin.fir.backend.native,org.jetbrains.k
 otlin.fir.backend.native.interop,org.jetbrains.kotlin.fir.backend.utils
 ,org.jetbrains.kotlin.fir.builder,org.jetbrains.kotlin.fir.caches,org.j
 etbrains.kotlin.fir.checkers,org.jetbrains.kotlin.fir.contracts,org.jet
 brains.kotlin.fir.contracts.builder,org.jetbrains.kotlin.fir.contracts.
 description,org.jetbrains.kotlin.fir.contracts.impl,org.jetbrains.kotli
 n.fir.declarations,org.jetbrains.kotlin.fir.declarations.builder,org.je
 tbrains.kotlin.fir.declarations.comparators,org.jetbrains.kotlin.fir.de
 clarations.impl,org.jetbrains.kotlin.fir.declarations.synthetic,org.jet
 brains.kotlin.fir.declarations.utils,org.jetbrains.kotlin.fir.descripto
 rs,org.jetbrains.kotlin.fir.deserialization,org.jetbrains.kotlin.fir.di
 agnostics,org.jetbrains.kotlin.fir.expressions,org.jetbrains.kotlin.fir
 .expressions.builder,org.jetbrains.kotlin.fir.expressions.impl,org.jetb
 rains.kotlin.fir.extensions,org.jetbrains.kotlin.fir.extensions.predica
 te,org.jetbrains.kotlin.fir.extensions.utils,org.jetbrains.kotlin.fir.i
 mpl,org.jetbrains.kotlin.fir.java,org.jetbrains.kotlin.fir.java.declara
 tions,org.jetbrains.kotlin.fir.java.deserialization,org.jetbrains.kotli
 n.fir.java.enhancement,org.jetbrains.kotlin.fir.java.scopes,org.jetbrai
 ns.kotlin.fir.java.symbols,org.jetbrains.kotlin.fir.lazy,org.jetbrains.
 kotlin.fir.lightTree,org.jetbrains.kotlin.fir.lightTree.converter,org.j
 etbrains.kotlin.fir.lightTree.fir,org.jetbrains.kotlin.fir.lightTree.fi
 r.modifier,org.jetbrains.kotlin.fir.modules,org.jetbrains.kotlin.fir.pi
 peline,org.jetbrains.kotlin.fir.plugin,org.jetbrains.kotlin.fir.referen
 ces,org.jetbrains.kotlin.fir.references.builder,org.jetbrains.kotlin.fi
 r.references.impl,org.jetbrains.kotlin.fir.renderer,org.jetbrains.kotli
 n.fir.resolve,org.jetbrains.kotlin.fir.resolve.calls,org.jetbrains.kotl
 in.fir.resolve.calls.candidate,org.jetbrains.kotlin.fir.resolve.calls.j
 s,org.jetbrains.kotlin.fir.resolve.calls.jvm,org.jetbrains.kotlin.fir.r
 esolve.calls.overloads,org.jetbrains.kotlin.fir.resolve.calls.stages,or
 g.jetbrains.kotlin.fir.resolve.calls.tower,org.jetbrains.kotlin.fir.res
 olve.dfa,org.jetbrains.kotlin.fir.resolve.dfa.cfg,org.jetbrains.kotlin.
 fir.resolve.diagnostics,org.jetbrains.kotlin.fir.resolve.inference,org.
 jetbrains.kotlin.fir.resolve.inference.model,org.jetbrains.kotlin.fir.r
 esolve.providers,org.jetbrains.kotlin.fir.resolve.providers.impl,org.je
 tbrains.kotlin.fir.resolve.scopes,org.jetbrains.kotlin.fir.resolve.subs
 titution,org.jetbrains.kotlin.fir.resolve.transformers,org.jetbrains.ko
 tlin.fir.resolve.transformers.body.resolve,org.jetbrains.kotlin.fir.res
 olve.transformers.contracts,org.jetbrains.kotlin.fir.resolve.transforme
 rs.mpp,org.jetbrains.kotlin.fir.resolve.transformers.plugin,org.jetbrai
 ns.kotlin.fir.scopes,org.jetbrains.kotlin.fir.scopes.impl,org.jetbrains
 .kotlin.fir.scopes.jvm,org.jetbrains.kotlin.fir.serialization,org.jetbr
 ains.kotlin.fir.serialization.constant,org.jetbrains.kotlin.fir.session
 ,org.jetbrains.kotlin.fir.session.environment,org.jetbrains.kotlin.fir.
 symbols,org.jetbrains.kotlin.fir.symbols.impl,org.jetbrains.kotlin.fir.
 types,org.jetbrains.kotlin.fir.types.builder,org.jetbrains.kotlin.fir.t
 ypes.impl,org.jetbrains.kotlin.fir.types.jvm,org.jetbrains.kotlin.fir.u
 til,org.jetbrains.kotlin.fir.utils.exceptions,org.jetbrains.kotlin.fir.
 visitors,org.jetbrains.kotlin.frontend.di,org.jetbrains.kotlin.frontend
 .java.di,org.jetbrains.kotlin.frontend.js.di,org.jetbrains.kotlin.idea,
 org.jetbrains.kotlin.idea.references,org.jetbrains.kotlin.incremental,o
 rg.jetbrains.kotlin.incremental.classpathDiff,org.jetbrains.kotlin.incr
 emental.classpathDiff.impl,org.jetbrains.kotlin.incremental.components,
 org.jetbrains.kotlin.incremental.dirtyFiles,org.jetbrains.kotlin.increm
 ental.impl,org.jetbrains.kotlin.incremental.javaInterop,org.jetbrains.k
 otlin.incremental.js,org.jetbrains.kotlin.incremental.multiproject,org.
 jetbrains.kotlin.incremental.parsing,org.jetbrains.kotlin.incremental.s
 napshots,org.jetbrains.kotlin.incremental.storage,org.jetbrains.kotlin.
 incremental.util,org.jetbrains.kotlin.inline,org.jetbrains.kotlin.io.op
 entelemetry.api,org.jetbrains.kotlin.io.opentelemetry.api.common,org.je
 tbrains.kotlin.io.opentelemetry.api.internal,org.jetbrains.kotlin.io.op
 entelemetry.api.logs,org.jetbrains.kotlin.io.opentelemetry.api.metrics,
 org.jetbrains.kotlin.io.opentelemetry.api.trace,org.jetbrains.kotlin.io
 .vavr,org.jetbrains.kotlin.io.vavr.collection,org.jetbrains.kotlin.io.v
 avr.concurrent,org.jetbrains.kotlin.io.vavr.control,org.jetbrains.kotli
 n.ir,org.jetbrains.kotlin.ir.backend.js,org.jetbrains.kotlin.ir.backend
 .js.checkers,org.jetbrains.kotlin.ir.backend.js.checkers.declarations,o
 rg.jetbrains.kotlin.ir.backend.js.checkers.expressions,org.jetbrains.ko
 tlin.ir.backend.js.dce,org.jetbrains.kotlin.ir.backend.js.export,org.je
 tbrains.kotlin.ir.backend.js.ic,org.jetbrains.kotlin.ir.backend.js.ir,o
 rg.jetbrains.kotlin.ir.backend.js.lower,org.jetbrains.kotlin.ir.backend
 .js.lower.calls,org.jetbrains.kotlin.ir.backend.js.lower.cleanup,org.je
 tbrains.kotlin.ir.backend.js.lower.coroutines,org.jetbrains.kotlin.ir.b
 ackend.js.lower.inline,org.jetbrains.kotlin.ir.backend.js.lower.seriali
 zation.ir,org.jetbrains.kotlin.ir.backend.js.transformers.irToJs,org.je
 tbrains.kotlin.ir.backend.js.utils,org.jetbrains.kotlin.ir.backend.js.u
 tils.serialization,org.jetbrains.kotlin.ir.backend.jvm,org.jetbrains.ko
 tlin.ir.backend.jvm.serialization,org.jetbrains.kotlin.ir.builders,org.
 jetbrains.kotlin.ir.builders.declarations,org.jetbrains.kotlin.ir.decla
 rations,org.jetbrains.kotlin.ir.declarations.impl,org.jetbrains.kotlin.
 ir.declarations.lazy,org.jetbrains.kotlin.ir.descriptors,org.jetbrains.
 kotlin.ir.expressions,org.jetbrains.kotlin.ir.expressions.impl,org.jetb
 rains.kotlin.ir.inline,org.jetbrains.kotlin.ir.inline.konan,org.jetbrai
 ns.kotlin.ir.interpreter,org.jetbrains.kotlin.ir.interpreter.builtins,o
 rg.jetbrains.kotlin.ir.interpreter.checker,org.jetbrains.kotlin.ir.inte
 rpreter.exceptions,org.jetbrains.kotlin.ir.interpreter.intrinsics,org.j
 etbrains.kotlin.ir.interpreter.preprocessor,org.jetbrains.kotlin.ir.int
 erpreter.proxy,org.jetbrains.kotlin.ir.interpreter.proxy.reflection,org
 .jetbrains.kotlin.ir.interpreter.stack,org.jetbrains.kotlin.ir.interpre
 ter.state,org.jetbrains.kotlin.ir.interpreter.state.reflection,org.jetb
 rains.kotlin.ir.interpreter.transformer,org.jetbrains.kotlin.ir.objcint
 erop,org.jetbrains.kotlin.ir.overrides,org.jetbrains.kotlin.ir.symbols,
 org.jetbrains.kotlin.ir.symbols.impl,org.jetbrains.kotlin.ir.types,org.
 jetbrains.kotlin.ir.types.impl,org.jetbrains.kotlin.ir.util,org.jetbrai
 ns.kotlin.ir.visitors,org.jetbrains.kotlin.it.unimi.dsi.fastutil,org.je
 tbrains.kotlin.it.unimi.dsi.fastutil.booleans,org.jetbrains.kotlin.it.u
 nimi.dsi.fastutil.bytes,org.jetbrains.kotlin.it.unimi.dsi.fastutil.char
 s,org.jetbrains.kotlin.it.unimi.dsi.fastutil.doubles,org.jetbrains.kotl
 in.it.unimi.dsi.fastutil.floats,org.jetbrains.kotlin.it.unimi.dsi.fastu
 til.ints,org.jetbrains.kotlin.it.unimi.dsi.fastutil.longs,org.jetbrains
 .kotlin.it.unimi.dsi.fastutil.objects,org.jetbrains.kotlin.it.unimi.dsi
 .fastutil.shorts,org.jetbrains.kotlin.javac,org.jetbrains.kotlin.javac.
 components,org.jetbrains.kotlin.javac.resolve,org.jetbrains.kotlin.java
 c.wrappers.symbols,org.jetbrains.kotlin.javac.wrappers.trees,org.jetbra
 ins.kotlin.javax.inject,org.jetbrains.kotlin.js,org.jetbrains.kotlin.js
 .analyze,org.jetbrains.kotlin.js.analyzer,org.jetbrains.kotlin.js.backe
 nd,org.jetbrains.kotlin.js.backend.ast,org.jetbrains.kotlin.js.backend.
 ast.metadata,org.jetbrains.kotlin.js.common,org.jetbrains.kotlin.js.con
 fig,org.jetbrains.kotlin.js.descriptorUtils,org.jetbrains.kotlin.js.inl
 ine.clean,org.jetbrains.kotlin.js.inline.util,org.jetbrains.kotlin.js.i
 nline.util.collectors,org.jetbrains.kotlin.js.naming,org.jetbrains.kotl
 in.js.parser,org.jetbrains.kotlin.js.parser.sourcemaps,org.jetbrains.ko
 tlin.js.patterns,org.jetbrains.kotlin.js.resolve,org.jetbrains.kotlin.j
 s.resolve.diagnostics,org.jetbrains.kotlin.js.sourceMap,org.jetbrains.k
 otlin.js.translate.context,org.jetbrains.kotlin.js.translate.utils,org.
 jetbrains.kotlin.js.util,org.jetbrains.kotlin.kapt3.diagnostic,org.jetb
 rains.kotlin.kdoc.lexer,org.jetbrains.kotlin.kdoc.parser,org.jetbrains.
 kotlin.kdoc.psi.api,org.jetbrains.kotlin.kdoc.psi.impl,org.jetbrains.ko
 tlin.konan,org.jetbrains.kotlin.konan.exec,org.jetbrains.kotlin.konan.f
 ile,org.jetbrains.kotlin.konan.library,org.jetbrains.kotlin.konan.libra
 ry.impl,org.jetbrains.kotlin.konan.properties,org.jetbrains.kotlin.kona
 n.target,org.jetbrains.kotlin.konan.util,org.jetbrains.kotlin.kotlinx.c
 ollections.immutable,org.jetbrains.kotlin.kotlinx.collections.immutable
 .implementations.immutableList,org.jetbrains.kotlin.kotlinx.collections
 .immutable.implementations.immutableMap,org.jetbrains.kotlin.kotlinx.co
 llections.immutable.implementations.immutableSet,org.jetbrains.kotlin.k
 otlinx.collections.immutable.implementations.persistentOrderedMap,org.j
 etbrains.kotlin.kotlinx.collections.immutable.implementations.persisten
 tOrderedSet,org.jetbrains.kotlin.kotlinx.collections.immutable.internal
 ,org.jetbrains.kotlin.lexer,org.jetbrains.kotlin.library,org.jetbrains.
 kotlin.library.abi,org.jetbrains.kotlin.library.abi.impl,org.jetbrains.
 kotlin.library.encodings,org.jetbrains.kotlin.library.impl,org.jetbrain
 s.kotlin.library.metadata,org.jetbrains.kotlin.library.metadata.impl,or
 g.jetbrains.kotlin.library.metadata.resolver,org.jetbrains.kotlin.libra
 ry.metadata.resolver.impl,org.jetbrains.kotlin.load.java,org.jetbrains.
 kotlin.load.java.components,org.jetbrains.kotlin.load.java.descriptors,
 org.jetbrains.kotlin.load.java.lazy,org.jetbrains.kotlin.load.java.lazy
 .descriptors,org.jetbrains.kotlin.load.java.lazy.types,org.jetbrains.ko
 tlin.load.java.sam,org.jetbrains.kotlin.load.java.sources,org.jetbrains
 .kotlin.load.java.structure,org.jetbrains.kotlin.load.java.structure.im
 pl,org.jetbrains.kotlin.load.java.structure.impl.classFiles,org.jetbrai
 ns.kotlin.load.java.structure.impl.source,org.jetbrains.kotlin.load.jav
 a.typeEnhancement,org.jetbrains.kotlin.load.kotlin,org.jetbrains.kotlin
 .load.kotlin.header,org.jetbrains.kotlin.load.kotlin.incremental,org.je
 tbrains.kotlin.load.kotlin.incremental.components,org.jetbrains.kotlin.
 metadata,org.jetbrains.kotlin.metadata.builtins,org.jetbrains.kotlin.me
 tadata.deserialization,org.jetbrains.kotlin.metadata.java,org.jetbrains
 .kotlin.metadata.js,org.jetbrains.kotlin.metadata.jvm,org.jetbrains.kot
 lin.metadata.jvm.deserialization,org.jetbrains.kotlin.metadata.jvm.seri
 alization,org.jetbrains.kotlin.metadata.serialization,org.jetbrains.kot
 lin.modules,org.jetbrains.kotlin.mpp,org.jetbrains.kotlin.name,org.jetb
 rains.kotlin.native.interop,org.jetbrains.kotlin.net.jpountz.lz4,org.je
 tbrains.kotlin.net.jpountz.util,org.jetbrains.kotlin.net.jpountz.util.l
 inux.aarch64,org.jetbrains.kotlin.net.jpountz.util.linux.amd64,org.jetb
 rains.kotlin.net.jpountz.util.linux.i386,org.jetbrains.kotlin.net.jpoun
 tz.util.linux.ppc64le,org.jetbrains.kotlin.net.jpountz.util.linux.s390x
 ,org.jetbrains.kotlin.net.jpountz.util.win32.amd64,org.jetbrains.kotlin
 .net.jpountz.xxhash,org.jetbrains.kotlin.one.util.streamex,org.jetbrain
 s.kotlin.org.apache.log4j,org.jetbrains.kotlin.org.apache.log4j.lf5.con
 fig,org.jetbrains.kotlin.org.codehaus.stax2,org.jetbrains.kotlin.org.co
 dehaus.stax2.ri,org.jetbrains.kotlin.org.codehaus.stax2.ri.typed,org.je
 tbrains.kotlin.org.codehaus.stax2.typed,org.jetbrains.kotlin.org.fuseso
 urce.jansi,org.jetbrains.kotlin.org.fusesource.jansi.internal,org.jetbr
 ains.kotlin.org.fusesource.jansi.internal.native.FreeBSD.x86,org.jetbra
 ins.kotlin.org.fusesource.jansi.internal.native.FreeBSD.x86_64,org.jetb
 rains.kotlin.org.fusesource.jansi.internal.native.Linux.arm,org.jetbrai
 ns.kotlin.org.fusesource.jansi.internal.native.Linux.arm64,org.jetbrain
 s.kotlin.org.fusesource.jansi.internal.native.Linux.armv6,org.jetbrains
 .kotlin.org.fusesource.jansi.internal.native.Linux.armv7,org.jetbrains.
 kotlin.org.fusesource.jansi.internal.native.Linux.ppc64,org.jetbrains.k
 otlin.org.fusesource.jansi.internal.native.Linux.x86,org.jetbrains.kotl
 in.org.fusesource.jansi.internal.native.Linux.x86_64,org.jetbrains.kotl
 in.org.fusesource.jansi.internal.native.Mac.arm64,org.jetbrains.kotlin.
 org.fusesource.jansi.internal.native.Mac.x86,org.jetbrains.kotlin.org.f
 usesource.jansi.internal.native.Mac.x86_64,org.jetbrains.kotlin.org.fus
 esource.jansi.internal.native.Windows.x86,org.jetbrains.kotlin.org.fuse
 source.jansi.internal.native.Windows.x86_64,org.jetbrains.kotlin.org.fu
 sesource.jansi.io,org.jetbrains.kotlin.org.jdom,org.jetbrains.kotlin.or
 g.jdom.filter2,org.jetbrains.kotlin.org.jdom.output,org.jetbrains.kotli
 n.org.jline.builtins,org.jetbrains.kotlin.org.jline.console,org.jetbrai
 ns.kotlin.org.jline.keymap,org.jetbrains.kotlin.org.jline.reader,org.je
 tbrains.kotlin.org.jline.reader.impl,org.jetbrains.kotlin.org.jline.rea
 der.impl.history,org.jetbrains.kotlin.org.jline.terminal,org.jetbrains.
 kotlin.org.jline.terminal.impl,org.jetbrains.kotlin.org.jline.terminal.
 spi,org.jetbrains.kotlin.org.jline.utils,org.jetbrains.kotlin.org.picoc
 ontainer,org.jetbrains.kotlin.parsing,org.jetbrains.kotlin.platform,org
 .jetbrains.kotlin.platform.js,org.jetbrains.kotlin.platform.jvm,org.jet
 brains.kotlin.platform.konan,org.jetbrains.kotlin.platform.wasm,org.jet
 brains.kotlin.plugin.references,org.jetbrains.kotlin.progress,org.jetbr
 ains.kotlin.protobuf,org.jetbrains.kotlin.psi,org.jetbrains.kotlin.psi.
 addRemoveModifier,org.jetbrains.kotlin.psi.codeFragmentUtil,org.jetbrai
 ns.kotlin.psi.debugText,org.jetbrains.kotlin.psi.findDocComment,org.jet
 brains.kotlin.psi.psiUtil,org.jetbrains.kotlin.psi.stubs,org.jetbrains.
 kotlin.psi.stubs.elements,org.jetbrains.kotlin.psi.stubs.impl,org.jetbr
 ains.kotlin.psi.synthetics,org.jetbrains.kotlin.psi.typeRefHelpers,org.
 jetbrains.kotlin.psi2ir,org.jetbrains.kotlin.psi2ir.descriptors,org.jet
 brains.kotlin.psi2ir.generators,org.jetbrains.kotlin.psi2ir.generators.
 fragments,org.jetbrains.kotlin.psi2ir.intermediate,org.jetbrains.kotlin
 .psi2ir.lazy,org.jetbrains.kotlin.psi2ir.preprocessing,org.jetbrains.ko
 tlin.psi2ir.transformations,org.jetbrains.kotlin.renderer,org.jetbrains
 .kotlin.resolve,org.jetbrains.kotlin.resolve.annotations,org.jetbrains.
 kotlin.resolve.bindingContextUtil,org.jetbrains.kotlin.resolve.calls,or
 g.jetbrains.kotlin.resolve.calls.callUtil,org.jetbrains.kotlin.resolve.
 calls.checkers,org.jetbrains.kotlin.resolve.calls.components,org.jetbra
 ins.kotlin.resolve.calls.components.candidate,org.jetbrains.kotlin.reso
 lve.calls.context,org.jetbrains.kotlin.resolve.calls.inference,org.jetb
 rains.kotlin.resolve.calls.inference.components,org.jetbrains.kotlin.re
 solve.calls.inference.constraintPosition,org.jetbrains.kotlin.resolve.c
 alls.inference.model,org.jetbrains.kotlin.resolve.calls.model,org.jetbr
 ains.kotlin.resolve.calls.mpp,org.jetbrains.kotlin.resolve.calls.result
 s,org.jetbrains.kotlin.resolve.calls.smartcasts,org.jetbrains.kotlin.re
 solve.calls.tasks,org.jetbrains.kotlin.resolve.calls.tower,org.jetbrain
 s.kotlin.resolve.calls.util,org.jetbrains.kotlin.resolve.checkers,org.j
 etbrains.kotlin.resolve.constants,org.jetbrains.kotlin.resolve.constant
 s.evaluate,org.jetbrains.kotlin.resolve.deprecation,org.jetbrains.kotli
 n.resolve.descriptorUtil,org.jetbrains.kotlin.resolve.diagnostics,org.j
 etbrains.kotlin.resolve.extensions,org.jetbrains.kotlin.resolve.inline,
 org.jetbrains.kotlin.resolve.jvm,org.jetbrains.kotlin.resolve.jvm.annot
 ations,org.jetbrains.kotlin.resolve.jvm.checkers,org.jetbrains.kotlin.r
 esolve.jvm.diagnostics,org.jetbrains.kotlin.resolve.jvm.extensions,org.
 jetbrains.kotlin.resolve.jvm.jvmSignature,org.jetbrains.kotlin.resolve.
 jvm.kotlinSignature,org.jetbrains.kotlin.resolve.jvm.modules,org.jetbra
 ins.kotlin.resolve.jvm.multiplatform,org.jetbrains.kotlin.resolve.jvm.p
 latform,org.jetbrains.kotlin.resolve.konan.diagnostics,org.jetbrains.ko
 tlin.resolve.konan.platform,org.jetbrains.kotlin.resolve.lazy,org.jetbr
 ains.kotlin.resolve.lazy.data,org.jetbrains.kotlin.resolve.lazy.declara
 tions,org.jetbrains.kotlin.resolve.lazy.descriptors,org.jetbrains.kotli
 n.resolve.multiplatform,org.jetbrains.kotlin.resolve.references,org.jet
 brains.kotlin.resolve.repl,org.jetbrains.kotlin.resolve.sam,org.jetbrai
 ns.kotlin.resolve.scopes,org.jetbrains.kotlin.resolve.scopes.optimizati
 on,org.jetbrains.kotlin.resolve.scopes.receivers,org.jetbrains.kotlin.r
 esolve.scopes.synthetic,org.jetbrains.kotlin.resolve.scopes.utils,org.j
 etbrains.kotlin.resolve.source,org.jetbrains.kotlin.resolve.typeBinding
 ,org.jetbrains.kotlin.serialization,org.jetbrains.kotlin.serialization.
 deserialization,org.jetbrains.kotlin.serialization.deserialization.buil
 tins,org.jetbrains.kotlin.serialization.deserialization.descriptors,org
 .jetbrains.kotlin.serialization.js,org.jetbrains.kotlin.serialization.j
 s.ast,org.jetbrains.kotlin.storage,org.jetbrains.kotlin.synthetic,org.j
 etbrains.kotlin.type,org.jetbrains.kotlin.types,org.jetbrains.kotlin.ty
 pes.checker,org.jetbrains.kotlin.types.error,org.jetbrains.kotlin.types
 .expressions,org.jetbrains.kotlin.types.expressions.typeInfoFactory,org
 .jetbrains.kotlin.types.expressions.unqualifiedSuper,org.jetbrains.kotl
 in.types.extensions,org.jetbrains.kotlin.types.model,org.jetbrains.kotl
 in.types.typeUtil,org.jetbrains.kotlin.types.typesApproximation,org.jet
 brains.kotlin.util,org.jetbrains.kotlin.util.capitalizeDecapitalize,org
 .jetbrains.kotlin.util.collectionUtils,org.jetbrains.kotlin.util.sliced
 Map,org.jetbrains.kotlin.util.vavr,org.jetbrains.kotlin.utils,org.jetbr
 ains.kotlin.utils.addToStdlib,org.jetbrains.kotlin.utils.concurrent.blo
 ck,org.jetbrains.kotlin.utils.exceptions,org.jetbrains.kotlin.utils.fil
 eUtils,org.jetbrains.kotlin.utils.kapt,org.jetbrains.kotlin.utils.repl,
 org.jetbrains.kotlin.utils.strings,org.jetbrains.kotlin.wasm.analyze,or
 g.jetbrains.kotlin.wasm.config,org.jetbrains.kotlin.wasm.ir,org.jetbrai
 ns.kotlin.wasm.ir.convertors,org.jetbrains.kotlin.wasm.ir.debug,org.jet
 brains.kotlin.wasm.ir.source.location,org.jetbrains.kotlin.wasm.resolve
 ,org.jetbrains.kotlin.wasm.resolve.diagnostics,org.jetbrains.kotlin.was
 m.util,com.aemkotlineditor.core.services.impl,com.aemkotlineditor.core.
 servlets;version="1.0",com.aemkotlineditor.core.activators,com.aemkotli
 neditor.core.jobs,com.aemkotlineditor.core.utils,lib,messages,misc,org.
 jetbrains.concurrency,org.jetbrains.org.objectweb.asm,org.jetbrains.org
 .objectweb.asm.commons,org.jetbrains.org.objectweb.asm.signature,org.je
 tbrains.org.objectweb.asm.tree,org.jetbrains.org.objectweb.asm.tree.ana
 lysis,org.jetbrains.org.objectweb.asm.util
Provide-Capability: osgi.service;objectClass:List<String>="com.aemkotlin
 editor.core.activators.FolderJobActivator";uses:="com.aemkotlineditor.c
 ore.activators",osgi.service;objectClass:List<String>="com.aemkotlinedi
 tor.core.services.JobBuilderService";uses:="com.aemkotlineditor.core.se
 rvices",osgi.service;objectClass:List<String>="com.aemkotlineditor.core
 .services.KotlinCompilerScriptingService";uses:="com.aemkotlineditor.co
 re.services",osgi.service;objectClass:List<String>="com.aemkotlineditor
 .core.services.ScriptingHost";uses:="com.aemkotlineditor.core.services"
 ,osgi.service;objectClass:List<String>="com.aemkotlineditor.core.servic
 es.SimpleKotlinScriptingService";uses:="com.aemkotlineditor.core.servic
 es",osgi.service;objectClass:List<String>="javax.servlet.Servlet";uses:
 ="javax.servlet",osgi.service;objectClass:List<String>="org.apache.slin
 g.event.jobs.consumer.JobConsumer";uses:="org.apache.sling.event.jobs.c
 onsumer"
Require-Capability: osgi.service;filter:="(objectClass=com.aemkotlinedit
 or.core.services.JobBuilderService)";effective:=active,osgi.service;fil
 ter:="(objectClass=com.aemkotlineditor.core.services.KotlinCompilerScri
 ptingService)";effective:=active,osgi.service;filter:="(objectClass=com
 .aemkotlineditor.core.services.ScriptingHost)";effective:=active,osgi.s
 ervice;filter:="(objectClass=com.aemkotlineditor.core.services.SimpleKo
 tlinScriptingService)";effective:=active,osgi.service;filter:="(objectC
 lass=org.apache.sling.api.resource.ResourceResolverFactory)";effective:
 =active,osgi.service;filter:="(objectClass=org.apache.sling.event.jobs.
 JobManager)";effective:=active,osgi.extender;filter:="(&(osgi.extender=
 osgi.component)(version>=1.5.0)(!(version>=2.0.0)))",osgi.ee;filter:="(
 &(osgi.ee=JavaSE)(version=11))"
Service-Component: OSGI-INF/com.aemkotlineditor.core.activators.FolderJo
 bActivator.xml,OSGI-INF/com.aemkotlineditor.core.jobs.KotlinJobConsumer
 .xml,OSGI-INF/com.aemkotlineditor.core.services.impl.JobBuilderServiceI
 mpl.xml,OSGI-INF/com.aemkotlineditor.core.services.impl.KotlinCompilerS
 criptingServiceImpl.xml,OSGI-INF/com.aemkotlineditor.core.services.impl
 .ScriptingHostImpl.xml,OSGI-INF/com.aemkotlineditor.core.services.impl.
 SimpleKotlinScriptingServiceImpl.xml,OSGI-INF/com.aemkotlineditor.core.
 servlets.JobStatusServlet.xml,OSGI-INF/com.aemkotlineditor.core.servlet
 s.KotlinCompilerTestServlet.xml,OSGI-INF/com.aemkotlineditor.core.servl
 ets.KotlinJobSubmissionServlet.xml,OSGI-INF/com.aemkotlineditor.core.se
 rvlets.SimpleKotlinTestServlet.xml

