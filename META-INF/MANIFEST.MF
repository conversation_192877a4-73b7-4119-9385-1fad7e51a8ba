Manifest-Version: 1.0
Created-By: Maven Archiver 3.4.0
Build-Jdk-Spec: 23
Bundle-Category: AEM Kotlin Editor
Bundle-ClassPath: .,lib/kotlin-stdlib.jar
Bundle-Description: Core bundle for AEM Kotlin Editor
Bundle-ManifestVersion: 2
Bundle-Name: AEM Kotlin Editor - Core
Bundle-SymbolicName: aem-kotlin-editor.core
Bundle-Version: 1.0.0.SNAPSHOT
Export-Package: com.aemkotlineditor.core.scripts;uses:="com.aemkotlinedi
 tor.core.exceptions,org.apache.sling.api.resource";version="1.0.0",com.
 aemkotlineditor.core.services;version="1.0";uses:="com.aemkotlineditor.
 core.exceptions,org.apache.sling.api.resource",com.aemkotlineditor.core
 .models;version="1.0";uses:="com.fasterxml.jackson.annotation",com.aemk
 otlineditor.core.exceptions;version="1.0.0",com.aemkotlineditor.core.se
 rvlets;version="1.0";uses:="javax.servlet,org.apache.sling.api,org.apac
 he.sling.api.servlets"
Import-Package: javax.script,org.slf4j;version="[1.7,2)",com.aemkotlined
 itor.core.exceptions;resolution:=optional,com.aemkotlineditor.core.mode
 ls;resolution:=optional;version="[1.0,2)",com.aemkotlineditor.core.scri
 pts;resolution:=optional,com.aemkotlineditor.core.services;resolution:=
 optional;version="[1.0,2)",com.aemkotlineditor.core.servlets;resolution
 :=optional;version="[1.0,2)",com.fasterxml.jackson.annotation;resolutio
 n:=optional;version="[2.17,3)",com.fasterxml.jackson.databind;resolutio
 n:=optional;version="[2.17,3)",java.io;resolution:=optional,java.lang;r
 esolution:=optional,java.lang.annotation;resolution:=optional,java.lang
 .invoke;resolution:=optional,java.lang.reflect;resolution:=optional,jav
 a.math;resolution:=optional,java.net;resolution:=optional,java.nio;reso
 lution:=optional,java.nio.charset;resolution:=optional,java.nio.file;re
 solution:=optional,java.nio.file.attribute;resolution:=optional,java.se
 curity;resolution:=optional,java.text;resolution:=optional,java.time;re
 solution:=optional,java.util;resolution:=optional,java.util.concurrent;
 resolution:=optional,java.util.concurrent.atomic;resolution:=optional,j
 ava.util.concurrent.locks;resolution:=optional,java.util.function;resol
 ution:=optional,java.util.regex;resolution:=optional,java.util.stream;r
 esolution:=optional,javax.servlet;resolution:=optional;version="[3.1,4)
 ",kotlin.reflect.jvm.internal;resolution:=optional,org.apache.commons.c
 odec.digest;resolution:=optional;version="[1.17,2)",org.apache.commons.
 io;resolution:=optional;version="[1.4,2)",org.apache.sling.api;resoluti
 on:=optional;version="[2.3,3)",org.apache.sling.api.resource;resolution
 :=optional;version="[2.13,3)",org.apache.sling.api.servlets;resolution:
 =optional;version="[2.4,3)",org.apache.sling.event.jobs;resolution:=opt
 ional;version="[2.0,3)",org.apache.sling.event.jobs.consumer;resolution
 :=optional;version="[1.3,2)"
Private-Package: kotlin,kotlin.annotation,kotlin.collections,kotlin.coll
 ections.builders,kotlin.collections.jdk8,kotlin.collections.unsigned,ko
 tlin.comparisons,kotlin.concurrent,kotlin.concurrent.atomics,kotlin.con
 current.internal,kotlin.contracts,kotlin.coroutines,kotlin.coroutines.c
 ancellation,kotlin.coroutines.intrinsics,kotlin.coroutines.jvm.internal
 ,kotlin.enums,kotlin.experimental,kotlin.internal,kotlin.internal.jdk7,
 kotlin.internal.jdk8,kotlin.io,kotlin.io.encoding,kotlin.io.path,kotlin
 .jdk7,kotlin.js,kotlin.jvm,kotlin.jvm.functions,kotlin.jvm.internal,kot
 lin.jvm.internal.markers,kotlin.jvm.internal.unsafe,kotlin.jvm.jdk8,kot
 lin.jvm.optionals,kotlin.math,kotlin.properties,kotlin.random,kotlin.ra
 ndom.jdk8,kotlin.ranges,kotlin.reflect,kotlin.sequences,kotlin.streams.
 jdk8,kotlin.system,kotlin.text,kotlin.text.jdk8,kotlin.time,kotlin.time
 .jdk8,kotlin.uuid,com.aemkotlineditor.core.services.impl,com.aemkotline
 ditor.core.activators,com.aemkotlineditor.core.jobs,com.aemkotlineditor
 .core.utils,lib
Provide-Capability: osgi.service;objectClass:List<String>="com.aemkotlin
 editor.core.activators.FolderJobActivator";uses:="com.aemkotlineditor.c
 ore.activators",osgi.service;objectClass:List<String>="com.aemkotlinedi
 tor.core.services.JobBuilderService";uses:="com.aemkotlineditor.core.se
 rvices",osgi.service;objectClass:List<String>="com.aemkotlineditor.core
 .services.ScriptingHost";uses:="com.aemkotlineditor.core.services",osgi
 .service;objectClass:List<String>="com.aemkotlineditor.core.services.Si
 mpleKotlinScriptingService";uses:="com.aemkotlineditor.core.services",o
 sgi.service;objectClass:List<String>="javax.servlet.Servlet";uses:="jav
 ax.servlet",osgi.service;objectClass:List<String>="org.apache.sling.eve
 nt.jobs.consumer.JobConsumer";uses:="org.apache.sling.event.jobs.consum
 er"
Require-Capability: osgi.service;filter:="(objectClass=com.aemkotlinedit
 or.core.services.JobBuilderService)";effective:=active,osgi.service;fil
 ter:="(objectClass=com.aemkotlineditor.core.services.ScriptingHost)";ef
 fective:=active,osgi.service;filter:="(objectClass=com.aemkotlineditor.
 core.services.SimpleKotlinScriptingService)";effective:=active,osgi.ser
 vice;filter:="(objectClass=org.apache.sling.api.resource.ResourceResolv
 erFactory)";effective:=active,osgi.service;filter:="(objectClass=org.ap
 ache.sling.event.jobs.JobManager)";effective:=active,osgi.extender;filt
 er:="(&(osgi.extender=osgi.component)(version>=1.5.0)(!(version>=2.0.0)
 ))",osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=11))"
Service-Component: OSGI-INF/com.aemkotlineditor.core.activators.FolderJo
 bActivator.xml,OSGI-INF/com.aemkotlineditor.core.jobs.KotlinJobConsumer
 .xml,OSGI-INF/com.aemkotlineditor.core.services.impl.JobBuilderServiceI
 mpl.xml,OSGI-INF/com.aemkotlineditor.core.services.impl.ScriptingHostIm
 pl.xml,OSGI-INF/com.aemkotlineditor.core.services.impl.SimpleKotlinScri
 ptingServiceImpl.xml,OSGI-INF/com.aemkotlineditor.core.servlets.JobStat
 usServlet.xml,OSGI-INF/com.aemkotlineditor.core.servlets.KotlinJobSubmi
 ssionServlet.xml,OSGI-INF/com.aemkotlineditor.core.servlets.SimpleKotli
 nTestServlet.xml

