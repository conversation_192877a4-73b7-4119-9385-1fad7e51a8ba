Manifest-Version: 1.0
Created-By: Maven Archiver 3.4.0
Build-Jdk-Spec: 23
Bundle-Category: AEM Kotlin Editor
Bundle-ClassPath: .,lib/kotlin-stdlib.jar,lib/kotlin-script-runtime.jar,
 lib/kotlin-scripting-jsr223.jar
Bundle-Description: Core bundle for AEM Kotlin Editor
Bundle-ManifestVersion: 2
Bundle-Name: AEM Kotlin Editor - Core
Bundle-SymbolicName: aem-kotlin-editor.core
Bundle-Version: 1.0.0.SNAPSHOT
Export-Package: com.aemkotlineditor.core.scripts;uses:="com.aemkotlinedi
 tor.core.exceptions,org.apache.sling.api.resource";version="1.0.0",com.
 aemkotlineditor.core.services;version="1.0";uses:="com.aemkotlineditor.
 core.exceptions,org.apache.sling.api.resource",com.aemkotlineditor.core
 .models;version="1.0";uses:="com.fasterxml.jackson.annotation",com.aemk
 otlineditor.core.exceptions;version="1.0.0",com.aemkotlineditor.core.se
 rvlets;version="1.0";uses:="javax.servlet,org.apache.sling.api,org.apac
 he.sling.api.servlets"
Import-Package: javax.script,org.slf4j;version="[1.7,2)",com.aemkotlined
 itor.core.exceptions;resolution:=optional,com.aemkotlineditor.core.mode
 ls;resolution:=optional;version="[1.0,2)",com.aemkotlineditor.core.scri
 pts;resolution:=optional,com.aemkotlineditor.core.services;resolution:=
 optional;version="[1.0,2)",com.aemkotlineditor.core.servlets;resolution
 :=optional;version="[1.0,2)",com.fasterxml.jackson.annotation;resolutio
 n:=optional;version="[2.17,3)",com.fasterxml.jackson.databind;resolutio
 n:=optional;version="[2.17,3)",java.io;resolution:=optional,java.lang;r
 esolution:=optional,java.lang.annotation;resolution:=optional,java.lang
 .invoke;resolution:=optional,java.lang.ref;resolution:=optional,java.la
 ng.reflect;resolution:=optional,java.math;resolution:=optional,java.net
 ;resolution:=optional,java.nio;resolution:=optional,java.nio.charset;re
 solution:=optional,java.nio.file;resolution:=optional,java.nio.file.att
 ribute;resolution:=optional,java.security;resolution:=optional,java.tex
 t;resolution:=optional,java.time;resolution:=optional,java.util;resolut
 ion:=optional,java.util.concurrent;resolution:=optional,java.util.concu
 rrent.atomic;resolution:=optional,java.util.concurrent.locks;resolution
 :=optional,java.util.function;resolution:=optional,java.util.regex;reso
 lution:=optional,java.util.stream;resolution:=optional,javax.servlet;re
 solution:=optional;version="[3.1,4)",org.apache.commons.codec.digest;re
 solution:=optional;version="[1.17,2)",org.apache.commons.io;resolution:
 =optional;version="[1.4,2)",org.apache.sling.api;resolution:=optional;v
 ersion="[2.3,3)",org.apache.sling.api.resource;resolution:=optional;ver
 sion="[2.13,3)",org.apache.sling.api.servlets;resolution:=optional;vers
 ion="[2.4,3)",org.apache.sling.event.jobs;resolution:=optional;version=
 "[2.0,3)",org.apache.sling.event.jobs.consumer;resolution:=optional;ver
 sion="[1.3,2)"
Private-Package: kotlin,kotlin.annotation,kotlin.collections,kotlin.coll
 ections.builders,kotlin.collections.jdk8,kotlin.collections.unsigned,ko
 tlin.comparisons,kotlin.concurrent,kotlin.concurrent.atomics,kotlin.con
 current.internal,kotlin.contracts,kotlin.coroutines,kotlin.coroutines.c
 ancellation,kotlin.coroutines.intrinsics,kotlin.coroutines.jvm.internal
 ,kotlin.enums,kotlin.experimental,kotlin.internal,kotlin.internal.jdk7,
 kotlin.internal.jdk8,kotlin.io,kotlin.io.encoding,kotlin.io.path,kotlin
 .jdk7,kotlin.js,kotlin.jvm,kotlin.jvm.functions,kotlin.jvm.internal,kot
 lin.jvm.internal.markers,kotlin.jvm.internal.unsafe,kotlin.jvm.jdk8,kot
 lin.jvm.optionals,kotlin.math,kotlin.properties,kotlin.random,kotlin.ra
 ndom.jdk8,kotlin.ranges,kotlin.reflect,kotlin.sequences,kotlin.streams.
 jdk8,kotlin.system,kotlin.text,kotlin.text.jdk8,kotlin.time,kotlin.time
 .jdk8,kotlin.uuid,kotlin.script.dependencies,kotlin.script.experimental
 .dependencies,kotlin.script.experimental.location,kotlin.script.extensi
 ons,kotlin.script.templates,kotlin.script.templates.standard,kotlin.scr
 ipt.experimental.jsr223,kotlin.reflect.full,kotlin.reflect.jvm,kotlin.r
 eflect.jvm.internal,kotlin.reflect.jvm.internal.calls,kotlin.reflect.jv
 m.internal.impl,kotlin.reflect.jvm.internal.impl.builtins,kotlin.reflec
 t.jvm.internal.impl.builtins.functions,kotlin.reflect.jvm.internal.impl
 .builtins.jvm,kotlin.reflect.jvm.internal.impl.descriptors,kotlin.refle
 ct.jvm.internal.impl.descriptors.annotations,kotlin.reflect.jvm.interna
 l.impl.descriptors.deserialization,kotlin.reflect.jvm.internal.impl.des
 criptors.impl,kotlin.reflect.jvm.internal.impl.descriptors.java,kotlin.
 reflect.jvm.internal.impl.descriptors.runtime.components,kotlin.reflect
 .jvm.internal.impl.descriptors.runtime.structure,kotlin.reflect.jvm.int
 ernal.impl.incremental,kotlin.reflect.jvm.internal.impl.incremental.com
 ponents,kotlin.reflect.jvm.internal.impl.km,kotlin.reflect.jvm.internal
 .impl.km.internal,kotlin.reflect.jvm.internal.impl.km.internal.common,k
 otlin.reflect.jvm.internal.impl.km.internal.extensions,kotlin.reflect.j
 vm.internal.impl.km.jvm,kotlin.reflect.jvm.internal.impl.km.jvm.interna
 l,kotlin.reflect.jvm.internal.impl.load.java,kotlin.reflect.jvm.interna
 l.impl.load.java.components,kotlin.reflect.jvm.internal.impl.load.java.
 descriptors,kotlin.reflect.jvm.internal.impl.load.java.lazy,kotlin.refl
 ect.jvm.internal.impl.load.java.lazy.descriptors,kotlin.reflect.jvm.int
 ernal.impl.load.java.lazy.types,kotlin.reflect.jvm.internal.impl.load.j
 ava.sources,kotlin.reflect.jvm.internal.impl.load.java.structure,kotlin
 .reflect.jvm.internal.impl.load.java.typeEnhancement,kotlin.reflect.jvm
 .internal.impl.load.kotlin,kotlin.reflect.jvm.internal.impl.load.kotlin
 .header,kotlin.reflect.jvm.internal.impl.metadata,kotlin.reflect.jvm.in
 ternal.impl.metadata.builtins,kotlin.reflect.jvm.internal.impl.metadata
 .deserialization,kotlin.reflect.jvm.internal.impl.metadata.jvm,kotlin.r
 eflect.jvm.internal.impl.metadata.jvm.deserialization,kotlin.reflect.jv
 m.internal.impl.metadata.serialization,kotlin.reflect.jvm.internal.impl
 .name,kotlin.reflect.jvm.internal.impl.platform,kotlin.reflect.jvm.inte
 rnal.impl.protobuf,kotlin.reflect.jvm.internal.impl.renderer,kotlin.ref
 lect.jvm.internal.impl.resolve,kotlin.reflect.jvm.internal.impl.resolve
 .calls.inference,kotlin.reflect.jvm.internal.impl.resolve.constants,kot
 lin.reflect.jvm.internal.impl.resolve.deprecation,kotlin.reflect.jvm.in
 ternal.impl.resolve.descriptorUtil,kotlin.reflect.jvm.internal.impl.res
 olve.jvm,kotlin.reflect.jvm.internal.impl.resolve.sam,kotlin.reflect.jv
 m.internal.impl.resolve.scopes,kotlin.reflect.jvm.internal.impl.resolve
 .scopes.receivers,kotlin.reflect.jvm.internal.impl.serialization,kotlin
 .reflect.jvm.internal.impl.serialization.deserialization,kotlin.reflect
 .jvm.internal.impl.serialization.deserialization.builtins,kotlin.reflec
 t.jvm.internal.impl.serialization.deserialization.descriptors,kotlin.re
 flect.jvm.internal.impl.storage,kotlin.reflect.jvm.internal.impl.types,
 kotlin.reflect.jvm.internal.impl.types.checker,kotlin.reflect.jvm.inter
 nal.impl.types.error,kotlin.reflect.jvm.internal.impl.types.extensions,
 kotlin.reflect.jvm.internal.impl.types.model,kotlin.reflect.jvm.interna
 l.impl.types.typeUtil,kotlin.reflect.jvm.internal.impl.types.typesAppro
 ximation,kotlin.reflect.jvm.internal.impl.util,kotlin.reflect.jvm.inter
 nal.impl.util.capitalizeDecapitalize,kotlin.reflect.jvm.internal.impl.u
 til.collectionUtils,kotlin.reflect.jvm.internal.impl.utils,kotlin.refle
 ct.jvm.internal.impl.utils.addToStdlib,com.aemkotlineditor.core.service
 s.impl,com.aemkotlineditor.core.activators,com.aemkotlineditor.core.job
 s,com.aemkotlineditor.core.utils,lib
Provide-Capability: osgi.service;objectClass:List<String>="com.aemkotlin
 editor.core.activators.FolderJobActivator";uses:="com.aemkotlineditor.c
 ore.activators",osgi.service;objectClass:List<String>="com.aemkotlinedi
 tor.core.services.JobBuilderService";uses:="com.aemkotlineditor.core.se
 rvices",osgi.service;objectClass:List<String>="com.aemkotlineditor.core
 .services.ScriptingHost";uses:="com.aemkotlineditor.core.services",osgi
 .service;objectClass:List<String>="com.aemkotlineditor.core.services.Si
 mpleKotlinScriptingService";uses:="com.aemkotlineditor.core.services",o
 sgi.service;objectClass:List<String>="javax.servlet.Servlet";uses:="jav
 ax.servlet",osgi.service;objectClass:List<String>="org.apache.sling.eve
 nt.jobs.consumer.JobConsumer";uses:="org.apache.sling.event.jobs.consum
 er"
Require-Capability: osgi.service;filter:="(objectClass=com.aemkotlinedit
 or.core.services.JobBuilderService)";effective:=active,osgi.service;fil
 ter:="(objectClass=com.aemkotlineditor.core.services.ScriptingHost)";ef
 fective:=active,osgi.service;filter:="(objectClass=com.aemkotlineditor.
 core.services.SimpleKotlinScriptingService)";effective:=active,osgi.ser
 vice;filter:="(objectClass=org.apache.sling.api.resource.ResourceResolv
 erFactory)";effective:=active,osgi.service;filter:="(objectClass=org.ap
 ache.sling.event.jobs.JobManager)";effective:=active,osgi.extender;filt
 er:="(&(osgi.extender=osgi.component)(version>=1.5.0)(!(version>=2.0.0)
 ))",osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=11))"
Service-Component: OSGI-INF/com.aemkotlineditor.core.activators.FolderJo
 bActivator.xml,OSGI-INF/com.aemkotlineditor.core.jobs.KotlinJobConsumer
 .xml,OSGI-INF/com.aemkotlineditor.core.services.impl.JobBuilderServiceI
 mpl.xml,OSGI-INF/com.aemkotlineditor.core.services.impl.ScriptingHostIm
 pl.xml,OSGI-INF/com.aemkotlineditor.core.services.impl.SimpleKotlinScri
 ptingServiceImpl.xml,OSGI-INF/com.aemkotlineditor.core.servlets.JobStat
 usServlet.xml,OSGI-INF/com.aemkotlineditor.core.servlets.KotlinJobSubmi
 ssionServlet.xml,OSGI-INF/com.aemkotlineditor.core.servlets.SimpleKotli
 nTestServlet.xml

