<?xml version="1.0" encoding="UTF-8"?>
<!--
 |  Copyright 2015 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- ====================================================================== -->
    <!-- P A R E N T  P R O J E C T  D E S C R I P T I O N                      -->
    <!-- ====================================================================== -->
    <parent>
        <groupId>com.aemkotlineditor</groupId>
        <artifactId>aem-kotlin-editor</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <!-- ====================================================================== -->
    <!-- P R O J E C T  D E S C R I P T I O N                                   -->
    <!-- ====================================================================== -->
    <artifactId>aem-kotlin-editor.ui.apps</artifactId>
    <packaging>content-package</packaging>
    <name>AEM Kotlin Editor - UI apps</name>
    <description>UI apps package for AEM Kotlin Editor</description>

    <!-- ====================================================================== -->
    <!-- B U I L D   D E F I N I T I O N                                        -->
    <!-- ====================================================================== -->
    <build>
        <sourceDirectory>src/main/content/jcr_root</sourceDirectory>
        <plugins>
            <!-- ====================================================================== -->
            <!-- V A U L T   P A C K A G E   P L U G I N S                              -->
            <!-- ====================================================================== -->
            <plugin>
                <groupId>org.apache.jackrabbit</groupId>
                <artifactId>filevault-package-maven-plugin</artifactId>
                <configuration>
                    <properties>
                        <cloudManagerTarget>none</cloudManagerTarget>
                    </properties>
                    <group>com.aemkotlineditor</group>
                    <name>aem-kotlin-editor.ui.apps</name>
                    <packageType>application</packageType>
                    <repositoryStructurePackages>
                        <repositoryStructurePackage>
                            <groupId>com.aemkotlineditor</groupId>
                            <artifactId>aem-kotlin-editor.ui.apps.structure</artifactId>
                        </repositoryStructurePackage>
                    </repositoryStructurePackages>
                    <dependencies>
                    </dependencies>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.day.jcr.vault</groupId>
                <artifactId>content-package-maven-plugin</artifactId>
                <extensions>true</extensions>
                <configuration>
                    <verbose>true</verbose>
                    <failOnError>true</failOnError>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.sling</groupId>
                <artifactId>htl-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>validate-htl-scripts</id>
                        <goals>
                            <goal>validate</goal>
                        </goals>
                        <phase>generate-sources</phase>
                        <configuration>
                            <generateJavaClasses>true</generateJavaClasses>
                            <generatedJavaClassesPrefix>org.apache.sling.scripting.sightly</generatedJavaClassesPrefix>
                            <sourceDirectory>${project.build.sourceDirectory}</sourceDirectory>
                            <allowedExpressionOptions>
                                <allowedExpressionOption>cssClassName</allowedExpressionOption>
                                <allowedExpressionOption>decoration</allowedExpressionOption>
                                <allowedExpressionOption>decorationTagName</allowedExpressionOption>
                                <allowedExpressionOption>wcmmode</allowedExpressionOption>
                            </allowedExpressionOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


    <!-- ====================================================================== -->
    <!-- D E P E N D E N C I E S                                                -->
    <!-- ====================================================================== -->
    <dependencies>
        <dependency>
            <groupId>com.aemkotlineditor</groupId>
            <artifactId>aem-kotlin-editor.core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.aemkotlineditor</groupId>
            <artifactId>aem-kotlin-editor.ui.frontend</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>


        <dependency>
            <groupId>com.aemkotlineditor</groupId>
            <artifactId>aem-kotlin-editor.ui.apps.structure</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
        </dependency>

        <dependency>
            <groupId>com.adobe.aem</groupId>
            <artifactId>aem-sdk-api</artifactId>
        </dependency>

        <!-- HTL dependencies needed for the HTL Maven Plugin source code generation -->
        <dependency>
            <groupId>org.apache.sling</groupId>
            <artifactId>org.apache.sling.scripting.sightly.runtime</artifactId>
            <version>1.2.4-1.4.0</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
