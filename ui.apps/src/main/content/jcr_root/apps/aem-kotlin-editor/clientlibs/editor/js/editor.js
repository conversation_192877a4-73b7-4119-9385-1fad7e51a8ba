(function() {
    'use strict';

    // The typo was on this line. The parenthesis after 'DOMContentLoaded' was incorrect.
    document.addEventListener('DOMContentLoaded', function() {
        // --- Element References ---
        const pathForm = document.getElementById('kotlin-job-form-path');
        const editorForm = document.getElementById('kotlin-job-form-editor');
        const statusForm = document.getElementById('status-check-form');
        const scriptPathInput = document.getElementById('scriptPath');
        const scriptTextInput = document.getElementById('scriptText');
        const jobIdInput = document.getElementById('jobId');
        const statusContainer = document.getElementById('status-container');
        const statusHeading = document.getElementById('status-heading');
        const outputArea = document.getElementById('job-output');
        const resultsContainer = document.getElementById('results-table-container');
        const loadMoreButton = document.getElementById('load-more-button');
        const tabs = document.querySelectorAll('.tab-link');
        const allExecuteButtons = document.querySelectorAll('.execute-button');

        // --- Constants and State ---
        const SUBMIT_URL = '/bin/kotlin/submitJob';
        const STATUS_URL = '/bin/kotlin/jobStatus';
        const PAGE_SIZE = 100;
        let currentJobId = null;
        let currentOffset = 0;
        let pollingIntervalId = null;

        // --- Event Listeners ---
        tabs.forEach(tab => tab.addEventListener('click', handleTabClick));
        pathForm.addEventListener('submit', handlePathSubmit);
        editorForm.addEventListener('submit', handleEditorSubmit);
        statusForm.addEventListener('submit', handleStatusCheck);
        loadMoreButton.addEventListener('click', handleLoadMore);

        // --- Event Handlers ---
        function handleTabClick(event) {
            const tabName = event.target.dataset.tab;
            document.querySelectorAll('.tab-content').forEach(tc => tc.classList.remove('active'));
            document.querySelectorAll('.tab-link').forEach(tl => tl.classList.remove('active'));
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        function handlePathSubmit(event) {
            event.preventDefault();
            const scriptPath = scriptPathInput.value;
            if (!scriptPath || scriptPath.trim() === '') {
                alert('Script path cannot be empty.');
                return;
            }
            const formData = new URLSearchParams();
            formData.append('scriptPath', scriptPath);
            submitJob(formData, 'Submitting job for path: ' + scriptPath);
        }

        function handleEditorSubmit(event) {
            event.preventDefault();
            const scriptText = scriptTextInput.value;
            if (!scriptText || scriptText.trim() === '') {
                alert('Script text cannot be empty.');
                return;
            }
            const formData = new URLSearchParams();
            formData.append('scriptText', scriptText);
            submitJob(formData, 'Submitting job from editor text...');
        }

        function handleStatusCheck(event) {
            event.preventDefault();
            const jobId = jobIdInput.value;
            if (jobId && jobId.trim()) {
                resetState(true); // Soft reset to keep the input value
                currentJobId = jobId.trim();
                startPolling(currentJobId);
            }
        }

        function handleLoadMore() {
            loadMoreButton.disabled = true;
            currentOffset += PAGE_SIZE;
            fetchStatus(currentJobId, currentOffset, true);
        }

        // --- Core Logic ---
        function submitJob(formData, initialMessage) {
            resetState(false); // Hard reset
            setButtonsDisabled(true);
            statusContainer.style.display = 'block';
            outputArea.textContent = initialMessage;

            // Get CSRF token using multiple fallback methods
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                displayError(new Error('Could not find CSRF token. Ensure the granite.csrf.standalone clientlib is included or CSRF protection is properly configured.'));
                return;
            }

            fetch(SUBMIT_URL, {
                method: 'POST',
                headers: {
                    'CSRF-Token': csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: formData
            })
            .then(handleFetchErrors)
            .then(data => {
                if (data.status === 'success') {
                    currentJobId = data.jobId;
                    jobIdInput.value = currentJobId;
                    startPolling(currentJobId);
                } else {
                    throw new Error(data.message || 'Unknown submission error.');
                }
            })
            .catch(displayError);
        }

        function startPolling(jobId) {
            if (pollingIntervalId) {
                clearInterval(pollingIntervalId);
            }
            statusContainer.style.display = 'block';
            resultsContainer.innerHTML = '';
            fetchStatus(jobId, 0, false);

            pollingIntervalId = setInterval(() => {
                // Only poll for the summary, don't append results
                fetchStatus(jobId, 0, false, true);
            }, 5000); // Poll every 5 seconds
        }

        function fetchStatus(jobId, offset, isAppending, isSummaryOnly = false) {
            loadMoreButton.style.display = 'none';
            // Don't fetch detailed results if we are just polling for the summary
            const limit = isSummaryOnly ? 0 : PAGE_SIZE;

            fetch(`${STATUS_URL}?jobId=${jobId}&offset=${offset}&limit=${limit}`)
                .then(handleFetchErrors)
                .then(data => {
                    updateStatusDisplay(data);

                    if (!isSummaryOnly) {
                        updateResultsTable(data.resultsPage, isAppending);
                    }

                    const isJobRunning = data.status !== 'COMPLETED' && data.status !== 'FAILED';
                    if (!isJobRunning) {
                        stopPolling();
                    }

                    // Show "Load More" if the job is running or if there are more results than currently shown
                    if (isJobRunning || (data.processedCount < data.totalItems)) {
                         loadMoreButton.style.display = 'block';
                         loadMoreButton.disabled = false;
                    }
                })
                .catch(err => {
                    displayError(err);
                    stopPolling();
                });
        }

        // --- UI Update Functions ---
        function updateStatusDisplay(data) {
            statusHeading.textContent = `Job Status [${data.jobId}]`;
            let progress = `Status: ${data.status}\nProgress: ${data.processedCount} / ${data.totalItems}  (Success: ${data.successCount}, Failed: ${data.failedCount})`;
            outputArea.textContent = progress;
        }

        function updateResultsTable(results, isAppending) {
            if (!results || results.length === 0) {
                 if (!isAppending) {
                    resultsContainer.innerHTML = '<p>No detailed results to display yet.</p>';
                 }
                 return;
            }

            let table = resultsContainer.querySelector('.results-table');
            if (!table || !isAppending) {
                currentOffset = 0; // Reset offset if we are rebuilding the table
                resultsContainer.innerHTML = `
                    <table class="results-table">
                        <thead>
                            <tr>
                                <th>Item ID</th>
                                <th>Status</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                `;
                table = resultsContainer.querySelector('.results-table');
            }

            const tbody = table.querySelector('tbody');
            results.forEach(item => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${escapeHtml(item.itemId)}</td>
                    <td class="status-${item.status}">${escapeHtml(item.status)}</td>
                    <td>${escapeHtml(item.message)}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function displayError(error) {
            outputArea.textContent = 'An error occurred:\n' + error.message;
            setButtonsDisabled(false);
        }

        // --- Utility Functions ---
        function getCsrfToken() {
            // Method 1: Try Granite CSRF token (most common in AEM)
            if (window.granite && window.granite.csrf && window.granite.csrf.token) {
                return window.granite.csrf.token;
            }

            // Method 2: Try to get from meta tag
            const metaToken = document.querySelector('meta[name="csrf-token"]');
            if (metaToken) {
                return metaToken.getAttribute('content');
            }

            // Method 3: Try to get from cookie (if configured)
            const cookieToken = getCookie('CSRF-Token');
            if (cookieToken) {
                return cookieToken;
            }

            // Method 4: Try to fetch from AEM's CSRF endpoint
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', '/libs/granite/csrf/token.json', false); // Synchronous for simplicity
                xhr.send();
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    return response.token;
                }
            } catch (e) {
                console.warn('Failed to fetch CSRF token from endpoint:', e);
            }

            return null;
        }

        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) {
                return parts.pop().split(';').shift();
            }
            return null;
        }

        function resetState(isSoft) {
            stopPolling();
            currentJobId = null;
            currentOffset = 0;
            if (!isSoft) {
                jobIdInput.value = '';
            }
            statusContainer.style.display = 'none';
            resultsContainer.innerHTML = '';
            loadMoreButton.style.display = 'none';
        }

        function stopPolling() {
            if (pollingIntervalId) {
                clearInterval(pollingIntervalId);
                pollingIntervalId = null;
            }
            setButtonsDisabled(false);
        }

        function setButtonsDisabled(isDisabled) {
            allExecuteButtons.forEach(b => b.disabled = isDisabled);
        }

        function handleFetchErrors(response) {
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw new Error(errorData.error || `HTTP error! Status: ${response.status}`);
                });
            }
            return response.json();
        }

        function escapeHtml(str) {
            if (!str) return '';
            const div = document.createElement('div');
            div.textContent = str;
            return div.innerHTML;
        }
    });
})();