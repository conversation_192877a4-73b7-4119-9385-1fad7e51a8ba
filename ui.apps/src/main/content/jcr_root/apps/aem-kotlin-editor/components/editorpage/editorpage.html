<!DOCTYPE HTML>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <title>AEM Kotlin Resumable Job Runner</title>

    <!-- Load Granite CSRF clientlib first -->
    <sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html"/>
    <sly data-sly-call="${clientlib.css @ categories='granite.csrf.standalone'}"/>
    <sly data-sly-call="${clientlib.js @ categories='granite.csrf.standalone'}"/>

    <!-- Load our editor clientlib after CSRF -->
    <sly data-sly-call="${clientlib.all @ categories='aem-kotlin-editor.editor'}"/>

    <!-- Add CSRF token as meta tag for fallback -->
    <meta name="csrf-token" content="${granite.csrf.token @ context='attribute'}"/>
</head>
<body class="coral--light">
<div class="editor-container">
    <h1>Kotlin Resumable Job Runner</h1>
    <p>Submit a script for durable, asynchronous execution or check the status of a previously submitted job.</p>

    <div class="tabs">
        <button class="tab-link active" data-tab="RunFromPath">Run from JCR Path</button>
        <button class="tab-link" data-tab="RunFromEditor">Run from Direct Input</button>
    </div>

    <div id="RunFromPath" class="tab-content active">
        <h3>Execute a script stored in the JCR</h3>
        <form id="kotlin-job-form-path" class="job-form">
            <div class="form-group">
                <label for="scriptPath">JCR Path to ResumableScript (.kts)</label>
                <input type="text" id="scriptPath" class="script-path-input" placeholder="/apps/my-scripts/my-migration.kts">
            </div>
            <button type="submit" class="execute-button">Submit Job from Path</button>
        </form>
    </div>

    <div id="RunFromEditor" class="tab-content">
        <h3>Execute script text directly</h3>
        <form id="kotlin-job-form-editor" class="job-form">
            <div class="form-group">
                <label for="scriptText">Script Text (must implement ResumableScript)</label>
                <textarea id="scriptText" rows="20" class="script-text-area" placeholder="import com.aemkotlineditor.core.scripts.ResumableScript..."></textarea>
            </div>
            <button type="submit" class="execute-button">Submit Script from Editor</button>
        </form>
    </div>

    <hr>

    <form id="status-check-form" class="job-form">
        <div class="form-group">
            <label for="jobId">Check Existing Job ID</label>
            <input type="text" id="jobId" class="script-path-input" placeholder="Enter Job ID to check status">
        </div>
        <button type="submit" class="execute-button">Check Status</button>
    </form>

    <div id="status-container" class="status-container" style="display: none;">
        <h3 id="status-heading">Job Status</h3>
        <div id="job-output" class="output-area"></div>
        <div id="results-table-container"></div>
        <button id="load-more-button" style="display: none;" class="execute-button">Load More Results</button>
    </div>
</div>
</body>
</html>