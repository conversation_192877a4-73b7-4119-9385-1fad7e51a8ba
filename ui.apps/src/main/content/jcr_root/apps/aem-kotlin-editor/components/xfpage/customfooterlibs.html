<!--/*
    Copyright 2019 Adobe

    Licensed under the Apache License, Version 2.0 (the "License");
    you may not use this file except in compliance with the License.
    You may obtain a copy of the License at

        http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.
*/-->

<sly data-sly-use.clientlib="/libs/granite/sightly/templates/clientlib.html">
    <sly data-sly-call="${clientlib.js @ categories='aem-kotlin-editor.base'}"/>
</sly>


<sly data-sly-use.page="com.adobe.cq.wcm.core.components.models.Page"
     data-sly-list="${page.htmlPageItems}">
    <script data-sly-test="${item.location.name == 'footer'}"
            data-sly-element="${item.element.name @ context='unsafe'}"
            data-sly-attribute="${item.attributes}"></script>
</sly>


