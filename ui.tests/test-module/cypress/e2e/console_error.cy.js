/*
 *  Copyright 2023 Adobe Systems Incorporated
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

import failOnConsoleError from 'cypress-fail-on-console-error';

failOnConsoleError();

describe('Console Error Test', () => {
    beforeEach(() => {
        cy.visit(Cypress.env('AEM_PUBLISH_URL'));
    });

    it('should not have any console errors', () => {
        // failOnConsoleError will fail the test if there are any console errors
        // it can be configured to match errors by regex or by message if required
    });
});
