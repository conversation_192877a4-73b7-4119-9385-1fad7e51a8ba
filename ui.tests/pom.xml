<?xml version="1.0" encoding="UTF-8"?>
<!--
 |  Copyright 2023 Adobe Systems Incorporated
 |
 |  Licensed under the Apache License, Version 2.0 (the "License");
 |  you may not use this file except in compliance with the License.
 |  You may obtain a copy of the License at
 |
 |      http://www.apache.org/licenses/LICENSE-2.0
 |
 |  Unless required by applicable law or agreed to in writing, software
 |  distributed under the License is distributed on an "AS IS" BASIS,
 |  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 |  See the License for the specific language governing permissions and
 |  limitations under the License.
--><!--
 | DO NOT MODIFY
--><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
    <artifactId>aem-kotlin-editor</artifactId>
    <groupId>com.aemkotlineditor</groupId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

    <groupId>com.adobe.cq.cloud</groupId>
    <version>0.0.1-SNAPSHOT</version>
    <artifactId>com.adobe.cq.cloud.testing.ui.cypress.tests</artifactId>
    <name>com.adobe.cq.cloud.testing.ui.cypress - UI Tests</name>
    <packaging>pom</packaging>
    <description>Cypress UI tests</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <REPORTS_PATH>${project.basedir}/target/reports</REPORTS_PATH>

        <!-- AEMaaCS UI testing convention properties -->
        <AEM_AUTHOR_URL>http://localhost:4502</AEM_AUTHOR_URL>
        <AEM_AUTHOR_USERNAME>admin</AEM_AUTHOR_USERNAME>
        <AEM_AUTHOR_PASSWORD>admin</AEM_AUTHOR_PASSWORD>
        <AEM_PUBLISH_USERNAME>admin</AEM_PUBLISH_USERNAME>
        <AEM_PUBLISH_PASSWORD>admin</AEM_PUBLISH_PASSWORD>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-clean-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.2.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-jar</id>
                        <phase>none</phase>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>assembly-ui-test-docker-context.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>ui-tests-check</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.github.eirslett</groupId>
                        <artifactId>frontend-maven-plugin</artifactId>
                        <version>${frontend-maven-plugin.version}</version>
                        <executions>
                            <execution>
                                <id>install node and npm</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>install-node-and-npm</goal>
                                </goals>
                                <configuration>
                                    <nodeVersion>v18.14.2</nodeVersion>
                                    <npmVersion>9.5.0</npmVersion>
                                    <installDirectory>test-module</installDirectory>
                                </configuration>
                            </execution>
                            <execution>
                                <id>lint</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>npm</goal>
                                </goals>
                                <configuration>
                                    <arguments>run lint</arguments>
                                </configuration>
                            </execution>
                        </executions>
                        <configuration>
                            <workingDirectory>test-module</workingDirectory>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>ui-tests-docker-build</id>
            <activation>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.6.0</version>
                        <executions>
                            <execution>
                            <id>docker-build</id>
                            <phase>package</phase>
                            <goals>
                                <goal>exec</goal>
                            </goals>
                            <configuration> 
                                <executable>docker</executable>
                                <workingDirectory>.</workingDirectory>
                                <arguments>
                                    <argument>build</argument>
                                    <argument>-t</argument>
                                    <argument>${project.groupId}-${project.artifactId}/ui.tests:${project.version}</argument>
                                    <argument>.</argument>
                                </arguments>
                            </configuration>
                            </execution>
                            <execution>
                            <id>docker-tag</id>
                            <phase>package</phase>
                            <goals>
                                <goal>exec</goal>
                            </goals>
                            <configuration> 
                                <executable>docker</executable>
                                <workingDirectory>.</workingDirectory>
                                <arguments>
                                    <argument>tag</argument>
                                    <argument>${project.groupId}-${project.artifactId}/ui.tests:${project.version}</argument>
                                    <argument>${project.groupId}-${project.artifactId}/ui.tests:latest</argument>
                                </arguments>
                            </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>ui-tests-docker-execution</id>
            <activation>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.6.0</version>
                        <executions>
                            <execution>
                                <id>docker-run</id>
                                <phase>test</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>docker</executable>
                                    <workingDirectory>.</workingDirectory>
                                    <arguments>
                                        <argument>run</argument>
                                        <argument>-eAEM_AUTHOR_URL=${AEM_AUTHOR_URL}</argument>
                                        <argument>-eAEM_AUTHOR_USERNAME=${AEM_AUTHOR_USERNAME}</argument>
                                        <argument>-eAEM_AUTHOR_PASSWORD=${AEM_AUTHOR_PASSWORD}</argument>
                                        <argument>-eAEM_PUBLISH_URL=${AEM_PUBLISH_URL}</argument>
                                        <argument>-eAEM_PUBLISH_USERNAME=${AEM_PUBLISH_USERNAME}</argument>
                                        <argument>-eAEM_PUBLISH_PASSWORD=${AEM_PUBLISH_PASSWORD}</argument>
                                        <argument>-eREPORTS_PATH=/tmp/reports</argument>
                                        <argument>-v${REPORTS_PATH}:/tmp/reports</argument>
                                        <argument>${project.groupId}-${project.artifactId}/ui.tests:latest</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
