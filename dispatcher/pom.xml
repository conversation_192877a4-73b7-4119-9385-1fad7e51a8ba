<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <!-- ====================================================================== -->
    <!-- P A R E N T  P R O J E C T  D E S C R I P T I O N                      -->
    <!-- ====================================================================== -->
    <parent>
        <groupId>com.aemkotlineditor</groupId>
        <artifactId>aem-kotlin-editor</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>aem-kotlin-editor.dispatcher.cloud</artifactId>
    <packaging>pom</packaging>
    <name>AEM Kotlin Editor - Dispatcher</name>
    <description>HTTP &amp; Dispatcher configurations for AEM Kotlin Editor</description>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptors>
                                <descriptor>assembly.xml</descriptor>
                            </descriptors>
                            <appendAssemblyId>false</appendAssemblyId>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <executions>
                    <!-- enforce that immutable files are not touched: https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure -->
                    <execution>
                        <id>enforce-checksum-of-immutable-files</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <!-- rules being inserted by archetype-pre-package.groovy -->
                                        <requireTextFileChecksum>
                                            <file>src/conf.d/available_vhosts/default.vhost</file>
                                            <checksum>758e78c452d8d93685eaaf6d78561828</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.d/available_vhosts/default.vhost</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.d/dispatcher_vhost.conf</file>
                                            <checksum>5e8550f34567ffa1a8c4f744852a8b61</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.d/dispatcher_vhost.conf</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.d/enabled_vhosts/vhosts.conf</file>
                                            <checksum>8e9af819b868d93df01b16d3487f3401</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.d/enabled_vhosts/vhosts.conf</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.d/rewrites/default_rewrite.rules</file>
                                            <checksum>1571c99af0456da2186442a5a6a072f1</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.d/rewrites/default_rewrite.rules</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/available_farms/default.farm</file>
                                            <checksum>c5e805cade08939226c3c69ecd6ebc3a</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/available_farms/default.farm</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/cache/default_invalidate.any</file>
                                            <checksum>1335157699f9ea9b51f72ab868c7e885</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/cache/default_invalidate.any</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/cache/default_rules.any</file>
                                            <checksum>bc9135f627dd2c813373950d7cb71af4</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/cache/default_rules.any</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/clientheaders/default_clientheaders.any</file>
                                            <checksum>f7c32e02723296939090f89b36b8e1dd</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/clientheaders/default_clientheaders.any</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/dispatcher.any</file>
                                            <checksum>f452e3f790c96de440dca7d2ae3630a6</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/dispatcher.any</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/enabled_farms/farms.any</file>
                                            <checksum>64d45e6fa1c7525a9a34aa4a7ccf0852</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/enabled_farms/farms.any</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/filters/default_filters.any</file>
                                            <checksum>89984e2bace630d1f2e160d1a01cf91f</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/filters/default_filters.any</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/renders/default_renders.any</file>
                                            <checksum>3c7472f635d35795ec270e7b0b40a07a</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/renders/default_renders.any</message>
                                        </requireTextFileChecksum>
                                        <requireTextFileChecksum>
                                            <file>src/conf.dispatcher.d/virtualhosts/default_virtualhosts.any</file>
                                            <checksum>dd1caafd65a7f5e249fbcdaa0e88ed9e</checksum>
                                            <type>md5</type>
                                            <message>There have been changes detected in a file which is supposed to be immutable according to https://docs.adobe.com/content/help/en/experience-manager-cloud-service/implementing/content-delivery/disp-overview.html#file-structure: src/conf.dispatcher.d/virtualhosts/default_virtualhosts.any</message>
                                        </requireTextFileChecksum>

                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
