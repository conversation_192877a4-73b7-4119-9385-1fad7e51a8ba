#
# This is the default publish farm definition for the dispatcher module. 
#
# DO NOT EDIT this file, your changes will have no impact on your deployment.
#
# Instead create a copy in the folder conf.dispatcher.d/available_farms and edit the copy.
# Finally, change to the directory conf.dispatcher.d/enabled_farms, remove the symbolic
# link for default_farm.any and create a symbolic link to your copy.
#

/publishfarm {
	# Dispatcher Debugging Flag
	# Include X-Cache-Info response header if X-Dispatcher-Info is in request header
	# /info "1"
	# client headers which should be passed through to the render instances
	# (feature supported since dispatcher build 2.6.3.5222)
	/clientheaders {
		$include "../clientheaders/clientheaders.any"
	}
	# hostname globbing for farm selection (virtual domain addressing)
	/virtualhosts {
		$include "../virtualhosts/virtualhosts.any"
	}
	# the load will be balanced among these render instances
	/renders {
		$include "../renders/default_renders.any"
	}
	# only handle the requests in the following acl. default is 'none'
	# the glob pattern is matched against the first request line
	/filter {
		$include "../filters/filters.any"
	}
	# if the package is installed on publishers to generate a list of all content with a vanityurl attached
	# this section will auto-allow the items to bypass the normal dispatcher filters
	# Reference: https://docs.adobe.com/docs/en/dispatcher/disp-config.html#Enabling%20Access%20to%20Vanity%20URLs%20-%20/vanity_urls
	# /vanity_urls {
	#	/url    "/libs/granite/dispatcher/content/vanityUrls.html"
	#	/file   "/tmp/vanity_urls"
	#	/delay  300
	#	/loadOnStartup 1
	# }
	# allow propagation of replication posts (should seldomly be used)
	/propagateSyndPost "0"
	# the cache is used to store requests from the renders for faster delivery
	# for a second time.
	/cache {
		# The cacheroot must be equal to the document root of the webserver
		/docroot "${DOCROOT}"
		# sets the level upto which files named ".stat" will be created in the
		# document root of the webserver. when an activation request for some
		# handle is received, only files within the same subtree are affected
		# by the invalidation.
		/statfileslevel "2"
		# caches also authorized data
		/allowAuthorized "0"
		# Flag indicating whether the dispatcher should serve stale content if
		# no remote server is available.
		/serveStaleOnError "1"
		# the rules define, which pages should be cached. please note that
		# - only GET requests are cached
		# - only requests with an extension are cached
		# - only requests without query parameters ( ? ) are cached
		# - only unauthorized pages are cached unless allowUnauthorized is set to 1
		/rules {
			$include "../cache/rules.any"
		}
		# the invalidate section defines those pages which are 'invalidated' after
		# any activation. please note that, the activated page itself and all 
		# related documents are flushed on an modification. for example: if the 
		# page /foo/bar is activated, all /foo/bar.* files are removed from the
		# cache.
		/invalidate {
			/0000 {
				/glob "*"
				/type "deny"
			}
			/0001 {
				/glob "*.html"
				/type "allow"
			}
			# to ensure that AEM forms HTMLs are not auto-invalidated due to invalidation of any other resource. It is supposed to be deleted only after its own activation.
			/0002
			{
				/glob "/content/forms/**/*.html"
				/type "deny"
			}
		}
		/allowedClients {
			$include "../cache/default_invalidate.any"
		}
		# The ignoreUrlParams section contains query string parameter names that
		# should be ignored when determining whether some request's output can be
		# cached or delivered from cache. Please only enable one of the examples below.
		# The recommended setting is to ignore all parameters and selectively allow them. e.g.
		# /ignoreUrlParams {
		# 	/0001 { /glob "*" /type "allow" }
		# 	/0002 { /glob "page" /type "deny" }
		# 	/0003 { /glob "product" /type "deny" }
		# }
		#
		# In this example configuration, the "q" parameter will be ignored as 
		# well as general marketing related parameters such as e.g. utm_campaign.
		# If any other parameters are specified the request gets forwarded to the publisher.
		# Marketing parameters can normally be ignored on most websites as they are tracked
		# through different means. 
		# /ignoreUrlParams {
		# 	/0001 { /glob "*" /type "deny" }
		# 	/0002 { /glob "q" /type "allow" }
		# 	$include "../cache/marketing_query_parameters.any"
		# }

		# Cache response headers next to a cached file. On the first request to
		# an uncached resource, all headers matching one of the values found here
		# are stored in a separate file, next to the cache file. On subsequent
		# requests to the cached resource, the stored headers are added to the
		# response.
		# Note, that file globbing characters are not allowed here.
		/headers {
			"Cache-Control"
			"Content-Disposition"
			"Content-Type"
			"Expires"
			"Last-Modified"
			"X-Content-Type-Options"
		}
		# A grace period defines the number of seconds a stale, auto-invalidated
		# resource may still be served from the cache after the last activation
		# occurring. Auto-invalidated resources are invalidated by any activation,
		# when their path matches the /invalidate section above. This setting
		# can be used in a setup, where a batch of activations would otherwise
		# repeatedly invalidate the entire cache.
		/gracePeriod "2"

		# Enable TTL evaluates the response headers from the backend, and if they
		# contain a Cache-Control max-age or Expires date, an auxiliary, empty file
		# next to the cache file is created, with modification time equal to the
		# expiry date. When the cache file is requested past the modification time
		# it is automatically re-requested from the backend.
		/enableTTL "1"
	}
}
