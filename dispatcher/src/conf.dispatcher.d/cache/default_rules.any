#
# These are the default cache rules.
#
# DO NOT EDIT this file, your changes will have no impact on your deployment.
#
# Instead modify rules.any.
#

# Put entries of items you do or don't want to cache in apaches doc root
# the globbing pattern to be compared against the url
# example: *             -> everything
#        : /foo/bar.*    -> only the /foo/bar documents
#        : /foo/bar/*    -> all pages below /foo/bar
#        : /foo/bar[./]* -> all pages below and /foo/bar itself
#        : *.html        -> all .html files
# Default allow all items to cache
/0000 {
	/glob "*"
	/type "allow"
}
# Don't cache csrf login tokens
/0001 {
    /glob "/libs/granite/csrf/token.json"
    /type "deny"
}

# AEM Screens cache rules
# Do not cache Screens channels json
/0010 {
	/glob "/content/screens/svc.channels.json"
	/type "deny"
}
/0011 {
	/glob "/content/screens/svc/channels.channels.json"
	/type "deny"
}
/0012 {
	/glob "/screens/channels.json"
	/type "deny"
}
