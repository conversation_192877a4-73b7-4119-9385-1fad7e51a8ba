#
# This is the default list of request headers to forward to AEM.
#
# DO NOT EDIT this file, your changes will have no impact on your deployment.
#
# Instead modify clientheaders.any.
#

"X-Forwarded-Proto"
"X-Forwarded-SSL-Certificate"
"X-Forwarded-SSL-Client-Cert"
"X-Forwarded-SSL"
"X-Forwarded-Protocol"
"CSRF-Token"
"referer"
"user-agent"
"from"
"content-type"
"content-length"
"accept-charset"
"accept-encoding"
"accept-language"
"accept"
"host"
"if-match"
"if-none-match"
"if-range"
"if-unmodified-since"
"max-forwards"
"range"
"cookie"
"depth"
"translate"
"expires"
"date"
"if"
"lock-token"
"x-expected-entity-length"
"destination"
"Sling-uploadmode"
"x-requested-with"
"If-Modified-Since"
"Authorization"
"x-request-id"
