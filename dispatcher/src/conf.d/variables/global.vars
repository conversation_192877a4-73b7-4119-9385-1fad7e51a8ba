#
# This file contains the variables defined for all virtual hosts
#

# Log level for the dispatcher
#
# Possible values are: <PERSON><PERSON><PERSON>, <PERSON>n, Info, Debug and Trace1
# Default value: Warn
#
# Define DISP_LOG_LEVEL Warn

# Log level for mod_rewrite
#
# Possible values are: <PERSON><PERSON>r, Warn, Info, Debug and Trace1 - Trace8
# Default value: Warn
#
# To debug your RewriteRules, it is recommended to raise your log
# level to Trace2.
#
# More information can be found at:
# https://httpd.apache.org/docs/current/mod/mod_rewrite.html#logging
#
# Define REWRITE_LOG_LEVEL Warn

# Disable default caching headers
#
# The following headers are set by default dispatcher configuration Expires, Cache-Control, Age.
# If you uncomment and define DISABLE_DEFAULT_CACHING variable these headers are not set any more
# and you can fully customize the caching behavior.
#
# Define DISABLE_DEFAULT_CACHING

# Enable caching for GraphQL persisted queries
#
# By default, GraphQL persisted query responses are not cached in dispatcher.
# If you uncomment and define CACHE_GRAPHQL_PERSISTED_QUERIES variable, then persisted query results
# will be cached in dispatcher. Using CORS, in that case, will require additional dispatcher configuration.
#
# Define CACHE_GRAPHQL_PERSISTED_QUERIES
