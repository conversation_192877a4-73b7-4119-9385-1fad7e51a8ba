#
# This is the default publish virtualhost definition for Apache. 
#
# DO NOT EDIT this file, your changes will have no impact on your deployment.
#
# Instead create a copy in the folder conf.d/available_vhosts and edit the copy.
# Finally, change to the directory conf.d/enabled_vhosts, remove the symbolic
# link for default.vhost and create a symbolic link to your copy.
#

# Include customer defined variables
Include conf.d/variables/custom.vars

<VirtualHost *:80>
	ServerName	"publish"
	# Put names of which domains are used for your published site/content here
	ServerAlias	 "*"
	# Use a document root that matches the one in conf.dispatcher.d/default.farm
	DocumentRoot "${DOCROOT}"
	# URI dereferencing algorithm is applied at <PERSON><PERSON>'s level, do not decode parameters here
	AllowEncodedSlashes NoDecode
	# Add header breadcrumbs for help in troubleshooting
	<IfModule mod_headers.c>
		Header add X-Vhost "publish"
	</IfModule>
	<Directory />
		<IfModule disp_apache2.c>
			# Some items cache with the wrong mime type
			# Use this option to use the name to auto-detect mime types when cached improperly
			ModMimeUsePathInfo On
			# Use this option to avoid cache poisoning
			# Sling will return /content/image.jpg as well as /content/image.jpg/ but apache can't search /content/image.jpg/ as a file
			# Apache will treat that like a directory.  This assures the last slash is never stored in cache
			DirectorySlash Off
			# Enable the dispatcher file handler for apache to fetch files from AEM
			SetHandler dispatcher-handler
		</IfModule>
		Options FollowSymLinks
		AllowOverride None
		# Insert filter
		SetOutputFilter DEFLATE
		# Don't compress images
		SetEnvIfNoCase Request_URI \.(?:gif|jpe?g|png)$ no-gzip dont-vary
		# Prevent clickjacking
		Header always append X-Frame-Options SAMEORIGIN
	</Directory>
	<Directory "${DOCROOT}">
		AllowOverride None
		Require all granted
	</Directory>
	<IfModule disp_apache2.c>
		# Enabled to allow rewrites to take affect and not be ignored by the dispatcher module
		DispatcherUseProcessedURL	On
		# Default setting to allow all errors to come from the aem instance
		DispatcherPassError		0
	</IfModule>
	<IfModule mod_rewrite.c>
		RewriteEngine	on
		Include conf.d/rewrites/rewrite.rules

		# Rewrite index page internally, pass through (PT)
		RewriteRule "^(/?)$" "/index.html" [PT]

	</IfModule>
</VirtualHost>
