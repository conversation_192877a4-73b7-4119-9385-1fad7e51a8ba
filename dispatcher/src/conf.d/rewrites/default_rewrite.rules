#
# These are the default rewrite rules.
#
# DO NOT EDIT this file, your changes will have no impact on your deployment.
#
# Instead modify your rewrite.rules file
#

# Examples:
# This ruleset would look for robots.txt and fetch it from the dam only if the domain is exampleco-dev.adobecqms.net
# RewriteCond %{SERVER_NAME} exampleco-dev.adobecqms.net [NC]
# RewriteRule ^/robots.txt$ /content/dam/exampleco/robots.txt [NC,PT]
# This ruleset would look for favicon.ico in exampleco's base dam folder if the domain is exampleco-brand1-dev.adobecqms.net
# RewriteCond %{SERVER_NAME} exampleco-brand1-dev.adobecqms.net [NC]
# RewriteRule ^/favicon.ico$ /content/dam/exampleco/favicon.ico [NC,PT]
# This ruleset would look for sitemap.xml and point it at the re-usable file in exampleco's general folder of their site pages
# RewriteCond %{SERVER_NAME} exampleco-brand2-dev.adobecqms.net [NC]
# RewriteRule ^/sitemap.xml$ /content/exampleco/general/sitemap.xml [NC,PT]
# This ruleset would look for logo.jpg on all sites and source it from exampleco's general folder
# RewriteRule ^/logo.jpg$ /content/dam/exampleco/general/logo.jpg [NC,PT]
# This ruleset is a vanity url that exampleco's contactus site that doesn't exist on our environment
# RewriteRule ^/contactus https://corp.exampleco.com/contactus.html [NC,R=301]

# Prevent X-FORWARDED-FOR spoofing
RewriteCond %{HTTP:X-Forwarded-For} !^$
RewriteCond %{HTTP:X-Forwarded-For} !^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}
RewriteCond %{HTTP:X-Forwarded-For} !^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))
RewriteRule .* - [F]

# Uncomment to force HSTS protection
# Header always set Strict-Transport-Security "max-age=63072000; includeSubdomains;"

# Block wordpress DDOS Attempts
RewriteRule ^.*xmlrpc.php - [F]
RewriteCond %{HTTP_USER_AGENT} ^.*wordpress [NC]
RewriteRule .* - [F]

# Block wp-login
RewriteRule ^.*wp-login - [F,NC,L]

# Allow the dispatcher to be able to cache persisted queries - they need an extension for the cache file
RewriteCond %{REQUEST_URI} ^/graphql/execute.json
RewriteRule ^/(.*)$ /$1;.json [PT]