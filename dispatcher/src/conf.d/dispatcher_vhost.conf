#
# This is a file provided by the runtime environment and only included for
# illustration purposes.
#
# DO NOT EDIT this file, your changes will have no impact on your deployment.
#

ServerName dispatcher

Include conf.d/variables/default.vars
Include conf.d/variables/global.vars


# WARNING!!! The probe paths below are INTERNAL and RESERVED - please DO NOT USE them in your virtual host configurations!

# Liveness probe URL
Alias "/system/probes/live" probes/live-status.json
# Startup probe URL
Alias "/system/probes/start" probes/startup-status.json

# internal probes endpoint
<LocationMatch "/system/probes">
    RewriteEngine Off
</LocationMatch>

<Directory "/etc/httpd/probes">
    SetHandler default-handler
    AllowOverride None
    Require all granted
</Directory>


#SKYOPS-13837: Proxy static frontend code requests through dispatcher
<IfDefine FRONTEND_SUPPORT>
    SSLProxyEngine on
    <LocationMatch "/libs/cq/frontend-static">
        RewriteRule "^/mnt/var/www/html/libs/cq/frontend-static(/[^\.].*)$" "%{env:FRONTEND_URI_PREFIX}$1?%{env:FRONTEND_URI_SUFFIX}" [P,L]
    </LocationMatch>
</IfDefine>

# **********: Allow the functional replication to access publish instance directly for dev and stage environments
<IfDefine ENVIRONMENT_DEV>
    <LocationMatch "/content/test-site/">
        ProxyPassMatch http://${AEM_HOST}:${AEM_PORT}
        RewriteEngine Off
    </LocationMatch>
</IfDefine>
<IfDefine ENVIRONMENT_STAGE>
    <LocationMatch "/content/test-site/">
        ProxyPassMatch http://${AEM_HOST}:${AEM_PORT}
        RewriteEngine Off
    </LocationMatch>
</IfDefine>

# If the module loads correctly then apply base settings for the module
<IfModule disp_apache2.c>
	# location of the configuration file. eg: 'conf/dispatcher.any'
	DispatcherConfig conf.dispatcher.d/dispatcher.any

	# Format for the dispatcher log file
	<IfDefine !LOG_X_REQUEST_ID>
		LogFormat "%t \"%m %{dispatcher:uri}e%q %H\" %{dispatcher:status}e %{dispatcher:cache}e [%{dispatcher:backend}e] %{ms}Tms \"%{Host}i\"" dispatcher
	</IfDefine>
	<IfDefine LOG_X_REQUEST_ID>
		LogFormat "%t \"%m %{dispatcher:uri}e%q %H\" %{dispatcher:status}e %{dispatcher:cache}e [%{dispatcher:backend}e] %{ms}Tms \"%{Host}i\" \"%{x-request-id}i\"" dispatcher
	</IfDefine>
	CustomLog "| /usr/sbin/rotatelogs -e -f -t logs/dispatcher.log 86400" dispatcher "expr=%{HANDLER} == 'dispatcher-handler'"

	# Log level for the dispatcher module
	LogLevel dispatcher_module:${DISP_LOG_LEVEL} rewrite_module:${REWRITE_LOG_LEVEL}

	# if turned to 1, request to / are not handled by the dispatcher
	# use the mod_alias then for the correct mapping
	DispatcherDeclineRoot Off

	# if turned to 1, the dispatcher uses the URL already processed
	# by handlers preceeding the dispatcher (i.e. mod_rewrite)
	# instead of the original one passed to the web server.
	DispatcherUseProcessedURL On
	# Default value of 0 but if its set to 1 then the dispatcher will have apache handle all errors
	# If set to a string of error numbers it will only hand off those errors to apache to handle
	# DispatcherPassError		403,404
	# DispatcherPassError		1

	# Setting to replace the Host header with the value of X-Forwarded-Host
	#
	# Possible values are: Off, On or a file name, containing the edge key to expect
	# Default: Off
	DispatcherUseForwardedHost ${FORWARDED_HOST_SETTING}

	# When enabled it removes Cache-Control headers set by mod_expires to unchacheable content
	DispatcherRestrictUncacheableContent On
</IfModule>

<IfDefine !DISABLE_DEFAULT_CACHING>
	<IfModule mod_expires.c>
		# Expire text/html after this many seconds
		ExpiresActive On
		ExpiresByType text/html A${EXPIRATION_TIME}
	</IfModule>
	Header unset Age
</IfDefine>

# SITES-11040 Do ProxyPassMatch, if caching for GraphQL Persisted Queries is not enabled
<IfDefine !CACHE_GRAPHQL_PERSISTED_QUERIES>
    # SITES-3659 Prevent re-encodes of URLs sent to GraphQL Persisted Queries API endpoint
	<LocationMatch "/graphql/execute.json/.*">
        ProxyPassMatch http://${AEM_HOST}:${AEM_PORT} nocanon
	</LocationMatch>
</IfDefine>

# managed redirect maps not configured (= backward-compatible)
<IfFile !opt-in/managed-rewrite-maps.yaml>
	# Legacy /systemready mapped to new Health probe URL /system/probes/health in AEM
	<Location "/systemready">
		ProxyPass http://${AEM_HOST}:${AEM_PORT}/system/probes/health
		RewriteEngine Off
	</Location>

	# Allow ingressroute checks through on /system/probes/health (regardless of dispatcher filters)
	<Location "/system/probes/health">
		ProxyPass http://${AEM_HOST}:${AEM_PORT}/system/probes/health
		RewriteEngine Off
	</Location>
</IfFile>
# managed redirect maps configured
<IfFile opt-in/managed-rewrite-maps.yaml>
	# check if traffic can be already allowed to pass (404/redirects not existing yet prevention)
	<IfFile /tmp/rewrites/ready>
		# Legacy /systemready mapped to new Health probe URL /system/probes/health in AEM
		<Location "/systemready">
			ProxyPass http://${AEM_HOST}:${AEM_PORT}/system/probes/health
			RewriteEngine Off
		</Location>

		# Allow ingressroute checks through on /system/probes/health (regardless of dispatcher filters)
		<Location "/system/probes/health">
			ProxyPass http://${AEM_HOST}:${AEM_PORT}/system/probes/health
			RewriteEngine Off
		</Location>
	</IfFile>
	# else forcing "403 Forbidden" for Health probes
	# "Any code greater than or equal to 200 and less than 400 indicates success. Any other code indicates failure."
	# as per https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
	<IfFile !/tmp/rewrites/ready>
	    <Location "/systemready">
	        Require all denied
	    </Location>
	    <Location "/system/probes/health">
	        Require all denied
	    </Location>
	</IfFile>
</IfFile>

# Readiness probe for K8S Endpoints also depends on AEM readiness probe
<Location "/system/probes/ready">
    ProxyPass http://${AEM_HOST}:${AEM_PORT}/system/probes/ready
    RewriteEngine Off
</Location>

# Allow access to CRXDE on dev environment
<IfDefine ENVIRONMENT_DEV>
    <LocationMatch "/crx/(de|server)/">
        ProxyPassMatch http://${AEM_HOST}:${AEM_PORT}
        RewriteEngine Off
    </LocationMatch>
</IfDefine>

# CQ-4287185: Allow access to magento reverse-proxy endpoint
<IfDefine COMMERCE>
	SSLProxyEngine on
	# CIF-2557 add ProxyRemote to tunnel reverse-proxy traffic through egress proxy if available
	<IfDefine HTTP_EGRESS_PROXY>
		ProxyRemote ${COMMERCE_ENDPOINT} "http://${AEM_HTTP_PROXY_HOST}:${AEM_HTTP_PROXY_PORT}"
	</IfDefine>
	<LocationMatch "/api/graphql(/default)?$">
		# Use an empty back reference from ProxyPassMatch to the LocationMatch regex to prevent the
		# original URL being appended to the proxy request
		ProxyPassMatch   ${COMMERCE_ENDPOINT}$2
		ProxyPassReverse ${COMMERCE_ENDPOINT}
		RewriteEngine 	 Off
		# CIF-2971: Experience Platform Connector cookie to header forwarding
		SetEnvIfNoCase Cookie "(^| )aep-segments-membership=([^;]*)" AEP_SEGMENTS_MEMBERSHIP=$2
		RequestHeader set aep-segments-membership "%{AEP_SEGMENTS_MEMBERSHIP}e" env=AEP_SEGMENTS_MEMBERSHIP
	</LocationMatch>
</IfDefine>
<IfDefine COMMERCE_ENDPOINT_2>
	SSLProxyEngine on
	<IfDefine HTTP_EGRESS_PROXY>
		ProxyRemote ${AEM_COMMERCE_ENDPOINT_2} "http://${AEM_HTTP_PROXY_HOST}:${AEM_HTTP_PROXY_PORT}"
	</IfDefine>
	<LocationMatch "/api/graphql/endpoint-2$">
		ProxyPassMatch   ${AEM_COMMERCE_ENDPOINT_2}$2
		ProxyPassReverse ${AEM_COMMERCE_ENDPOINT_2}
		RewriteEngine 	 Off
		SetEnvIfNoCase Cookie "(^| )aep-segments-membership=([^;]*)" AEP_SEGMENTS_MEMBERSHIP=$2
		RequestHeader set aep-segments-membership "%{AEP_SEGMENTS_MEMBERSHIP}e" env=AEP_SEGMENTS_MEMBERSHIP
	</LocationMatch>
</IfDefine>
<IfDefine COMMERCE_ENDPOINT_3>
	SSLProxyEngine on
	<IfDefine HTTP_EGRESS_PROXY>
		ProxyRemote ${AEM_COMMERCE_ENDPOINT_3} "http://${AEM_HTTP_PROXY_HOST}:${AEM_HTTP_PROXY_PORT}"
	</IfDefine>
	<LocationMatch "/api/graphql/endpoint-3$">
		ProxyPassMatch	 ${AEM_COMMERCE_ENDPOINT_3}$2
		ProxyPassReverse ${AEM_COMMERCE_ENDPOINT_3}
		RewriteEngine 	 Off
		SetEnvIfNoCase Cookie "(^| )aep-segments-membership=([^;]*)" AEP_SEGMENTS_MEMBERSHIP=$2
		RequestHeader set aep-segments-membership "%{AEP_SEGMENTS_MEMBERSHIP}e" env=AEP_SEGMENTS_MEMBERSHIP
	</LocationMatch>
</IfDefine>
<IfDefine COMMERCE_ENDPOINT_4>
	SSLProxyEngine on
	<IfDefine HTTP_EGRESS_PROXY>
		ProxyRemote ${AEM_COMMERCE_ENDPOINT_4} "http://${AEM_HTTP_PROXY_HOST}:${AEM_HTTP_PROXY_PORT}"
	</IfDefine>
	<LocationMatch "/api/graphql/endpoint-4$">
		ProxyPassMatch	 ${AEM_COMMERCE_ENDPOINT_4}$2
		ProxyPassReverse ${AEM_COMMERCE_ENDPOINT_4}
		RewriteEngine 	 Off
		SetEnvIfNoCase Cookie "(^| )aep-segments-membership=([^;]*)" AEP_SEGMENTS_MEMBERSHIP=$2
		RequestHeader set aep-segments-membership "%{AEP_SEGMENTS_MEMBERSHIP}e" env=AEP_SEGMENTS_MEMBERSHIP
	</LocationMatch>
</IfDefine>
<IfDefine COMMERCE_ENDPOINT_5>
	SSLProxyEngine on
	<IfDefine HTTP_EGRESS_PROXY>
		ProxyRemote ${AEM_COMMERCE_ENDPOINT_5} "http://${AEM_HTTP_PROXY_HOST}:${AEM_HTTP_PROXY_PORT}"
	</IfDefine>
	<LocationMatch "/api/graphql/endpoint-5$">
		ProxyPassMatch	 ${AEM_COMMERCE_ENDPOINT_5}$2
		ProxyPassReverse ${AEM_COMMERCE_ENDPOINT_5}
		RewriteEngine 	 Off
		SetEnvIfNoCase Cookie "(^| )aep-segments-membership=([^;]*)" AEP_SEGMENTS_MEMBERSHIP=$2
		RequestHeader set aep-segments-membership "%{AEP_SEGMENTS_MEMBERSHIP}e" env=AEP_SEGMENTS_MEMBERSHIP
	</LocationMatch>
</IfDefine>

# ASSETS-10359 Prevent rewrites and filtering of Delivery API URLs
<LocationMatch "^/adobe/dynamicmedia/deliver/.*">
    ProxyPassMatch http://${AEM_HOST}:${AEM_PORT}
    RewriteEngine Off
</LocationMatch>

# Disable access to default CGI scripts
<Directory "/var/www/localhost/cgi-bin">
    AllowOverride None
    Options None
    Require all denied
</Directory>

# internal metadata endpoint
Alias "/gitinit-status" metadata/gitinit-status.json

<LocationMatch "/gitinit-status">
    RewriteEngine Off
</LocationMatch>

<Directory "/etc/httpd/metadata">
	SetHandler default-handler
    AllowOverride None
    Require expr "%{HTTP_HOST} == '${POD_NAME}'"
</Directory>

# Dedicated vhost for Adobe proxy testing:
<VirtualHost *:80>
	ServerName	"test.proxy"
	# possibility to make overrides before directives in this vhost
	IncludeOptional conf.d/includes/first-listed-vhost.pre.includes
	# since this vhost is first-listed one, this setting influences other vhosts - see https://httpd.apache.org/docs/2.4/mod/core.html#limitrequestfieldsize
	LimitRequestFieldSize 32768
	DocumentRoot /var/www/localhost/htdocs
	AllowEncodedSlashes NoDecode
	<IfModule mod_headers.c>
		Header add X-Vhost "test.proxy"
	</IfModule>
	<Directory "/var/www/localhost/htdocs">
		Options Indexes FollowSymLinks
		AllowOverride None
		Require all granted
	</Directory>

	# SKYOPS-49434: Allow EaaS to access publish instance directly for dev and stage environments when test.proxy vhost is requested
	<IfDefine ENVIRONMENT_DEV>
		<LocationMatch "/">
			ProxyPassMatch http://${AEM_HOST}:${AEM_PORT}
			RewriteEngine Off
		</LocationMatch>
	</IfDefine>
	<IfDefine ENVIRONMENT_STAGE>
		<LocationMatch "/">
			ProxyPassMatch http://${AEM_HOST}:${AEM_PORT}
			RewriteEngine Off
		</LocationMatch>
	</IfDefine>
	# 403 Forbidden on prod
	<IfDefine ENVIRONMENT_PROD>
		<IfModule mod_rewrite.c>
			RewriteEngine	on
			RewriteRule ^ - [F]
		</IfModule>
	</IfDefine>
	# possibility to make overrides after directives in this vhost
	IncludeOptional conf.d/includes/first-listed-vhost.post.includes
</VirtualHost>

# Customer's vhosts:
Include conf.d/enabled_vhosts/*.vhost

# Create a catch-all vhost
# A catch-all is a safe place for un-matched hostnames to land.
# This prevents someone pointing an-unwanted DNS record at your site and loading your pages.
# Example: yoursitesucks.com (CNAME) -> yourelbaddressQKAWZM8H-208090978.us-east-1.elb.amazonaws.com
# This host will accept any hostname and with a rewrite rule load the same index page giving away no details as to what they are hitting
# That way bots and hackers won't know what purpose a random IP listening on webports is really doing.
# Hitting the catch all doesn't let them know the customer is ExampleCo.com etc..
<VirtualHost *:80>
	ServerName unmatched-host-catch-all
	ServerAlias "*"
	# Azure traffic manager will hit here so lets have a custom log for that
	SetEnvIf User-agent .*Azure\sTraffic\sManager.* trafficmanager
	CustomLog logs/healthcheck_access_log combined env=trafficmanager
	CustomLog logs/httpd_access.log combined env=!trafficmanager

	# Specify where the catch all html files live
	DocumentRoot /var/www/localhost/htdocs
	# Add some visible targets AKA breadcrumbs that you can see in your browser dev tools or curl -I command
	<Directory "/var/www/localhost/htdocs">
		Options Indexes FollowSymLinks
		AllowOverride None
		Require all granted
	</Directory>
	<IfModule mod_headers.c>
		Header always add X-Vhost catch-all
	</IfModule>
	<IfModule mod_rewrite.c>
		RewriteEngine on
		RewriteRule ^/* /index.html [PT,L,NC]
	</IfModule>
</VirtualHost>

# We want to make sure the apache versions are hidden so avoid possible attack vectors
ServerSignature Off
ServerTokens Prod
